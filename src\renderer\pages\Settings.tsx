import React, { useState } from 'react';
import { Settings as SettingsIcon, MessageCircle, Facebook, Info, Save } from 'lucide-react';

const Settings: React.FC = () => {
  const [gymSettings, setGymSettings] = useState({
    gymName: 'نادي اللياقة البدنية',
    gymAddress: '',
    gymPhone: '',
    gymEmail: '',
    currency: 'ريال'
  });

  const handleSaveSettings = () => {
    // سيتم ربطها بقاعدة البيانات لاحقاً
    alert('تم حفظ الإعدادات بنجاح');
  };

  const openWhatsApp = () => {
    window.open('https://wa.me/966225396729', '_blank');
  };

  const openFacebook = () => {
    window.open('https://web.facebook.com/profile.php?id=61578888731370', '_blank');
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">الإعدادات</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* إعدادات الجيم */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="card-header">
            <h2 className="card-title flex items-center">
              <SettingsIcon size={20} className="ml-2" />
              إعدادات الجيم
            </h2>
          </div>

          <div className="space-y-4">
            <div className="form-group">
              <label className="form-label">اسم الجيم</label>
              <input
                type="text"
                value={gymSettings.gymName}
                onChange={(e) => setGymSettings({...gymSettings, gymName: e.target.value})}
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label className="form-label">العنوان</label>
              <input
                type="text"
                value={gymSettings.gymAddress}
                onChange={(e) => setGymSettings({...gymSettings, gymAddress: e.target.value})}
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label className="form-label">رقم الهاتف</label>
              <input
                type="text"
                value={gymSettings.gymPhone}
                onChange={(e) => setGymSettings({...gymSettings, gymPhone: e.target.value})}
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label className="form-label">البريد الإلكتروني</label>
              <input
                type="email"
                value={gymSettings.gymEmail}
                onChange={(e) => setGymSettings({...gymSettings, gymEmail: e.target.value})}
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label className="form-label">العملة</label>
              <select
                value={gymSettings.currency}
                onChange={(e) => setGymSettings({...gymSettings, currency: e.target.value})}
                className="form-select"
              >
                <option value="ريال">ريال سعودي</option>
                <option value="درهم">درهم إماراتي</option>
                <option value="دينار">دينار كويتي</option>
                <option value="جنيه">جنيه مصري</option>
              </select>
            </div>

            <button
              onClick={handleSaveSettings}
              className="btn btn-primary w-full flex items-center justify-center"
            >
              <Save size={20} className="ml-2" />
              حفظ الإعدادات
            </button>
          </div>
        </div>

        {/* الدعم والمساعدة */}
        <div className="space-y-6">
          {/* أزرار التواصل */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="card-header">
              <h2 className="card-title">التواصل والدعم</h2>
            </div>

            <div className="space-y-4">
              <button
                onClick={openWhatsApp}
                className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-4 rounded-lg flex items-center justify-center transition-colors duration-200"
              >
                <MessageCircle size={20} className="ml-2" />
                الدعم الفني
              </button>

              <button
                onClick={openFacebook}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg flex items-center justify-center transition-colors duration-200"
              >
                <Facebook size={20} className="ml-2" />
                متابعة صفحتنا
              </button>
            </div>
          </div>

          {/* معلومات التطبيق */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="card-header">
              <h2 className="card-title flex items-center">
                <Info size={20} className="ml-2" />
                حول التطبيق
              </h2>
            </div>

            <div className="space-y-4 text-center">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">نظام إدارة الجيم</h3>
                <p className="text-gray-600">الإصدار 1.0.0</p>
              </div>

              <div className="border-t border-gray-200 pt-4">
                <p className="text-sm text-gray-600 mb-2">المطور:</p>
                <p className="font-medium text-gray-900">Eng/ Hossam Osama</p>
              </div>

              <div className="border-t border-gray-200 pt-4">
                <p className="text-sm text-gray-600 mb-2">تطوير:</p>
                <p className="font-bold text-blue-600 text-lg">H - TECH</p>
              </div>

              <div className="border-t border-gray-200 pt-4">
                <p className="text-xs text-gray-500">
                  جميع الحقوق محفوظة © 2024
                </p>
              </div>
            </div>
          </div>

          {/* النسخ الاحتياطي */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="card-header">
              <h2 className="card-title">النسخ الاحتياطي</h2>
            </div>

            <div className="space-y-4">
              <button className="btn btn-secondary w-full">
                إنشاء نسخة احتياطية
              </button>

              <button className="btn btn-outline w-full border-gray-300 text-gray-700">
                استيراد نسخة احتياطية
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
