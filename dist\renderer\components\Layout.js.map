{"version": 3, "file": "Layout.js", "sourceRoot": "", "sources": ["../../../src/renderer/components/Layout.tsx"], "names": [], "mappings": ";;;AAAA,iCAAwC;AACxC,uDAAqD;AACrD,+CAYsB;AAMtB,MAAM,MAAM,GAA0B,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;IACrD,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IACtD,MAAM,QAAQ,GAAG,IAAA,8BAAW,GAAE,CAAC;IAE/B,MAAM,SAAS,GAAG;QAChB,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,mBAAI,EAAE,KAAK,EAAE,aAAa,EAAE;QAC/C,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,oBAAK,EAAE,KAAK,EAAE,SAAS,EAAE;QACnD,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,yBAAU,EAAE,KAAK,EAAE,YAAY,EAAE;QACjE,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,uBAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;QACxD,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,wBAAS,EAAE,KAAK,EAAE,UAAU,EAAE;QACzD,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,uBAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;QACxD,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,yBAAU,EAAE,KAAK,EAAE,SAAS,EAAE;QACxD,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,2BAAY,EAAE,KAAK,EAAE,QAAQ,EAAE;QACtD,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,uBAAQ,EAAE,KAAK,EAAE,WAAW,EAAE;KAC1D,CAAC;IAEF,MAAM,QAAQ,GAAG,CAAC,IAAY,EAAE,EAAE;QAChC,OAAO,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC;IACpC,CAAC,CAAC;IAEF,OAAO,CACL,iCAAK,SAAS,EAAC,2BAA2B,aAExC,iCAAK,SAAS,EAAE,kEACd,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,kBAClC,sFAAsF,aAGpF,iCAAK,SAAS,EAAC,oEAAoE,aACjF,+BAAI,SAAS,EAAC,mBAAmB,uGAAsB,EACvD,mCACE,OAAO,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,EACpC,SAAS,EAAC,WAAW,YAErB,uBAAC,gBAAC,IAAC,IAAI,EAAE,EAAE,GAAI,GACR,IACL,EAGN,gCAAK,SAAS,EAAC,MAAM,YAClB,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;4BACtB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;4BACvB,OAAO,CACL,wBAAC,uBAAI,IAEH,EAAE,EAAE,IAAI,CAAC,IAAI,EACb,SAAS,EAAE,iHACT,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,qDAAqD,CAAC,CAAC,CAAC,EAChF,EAAE,EACF,OAAO,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,aAEpC,uBAAC,IAAI,IAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAC,MAAM,GAAG,EACnC,iCAAM,SAAS,EAAC,aAAa,YAAE,IAAI,CAAC,KAAK,GAAQ,KAR5C,IAAI,CAAC,IAAI,CAST,CACR,CAAC;wBACJ,CAAC,CAAC,GACE,EAGN,gCAAK,SAAS,EAAC,wEAAwE,YACrF,wIAA4B,GACxB,IACF,EAGN,iCAAK,SAAS,EAAC,+CAA+C,aAE5D,mCAAQ,SAAS,EAAC,6CAA6C,YAC7D,iCAAK,SAAS,EAAC,6CAA6C,aAC1D,mCACE,OAAO,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EACnC,SAAS,EAAC,6CAA6C,YAEvD,uBAAC,mBAAI,IAAC,IAAI,EAAE,EAAE,GAAI,GACX,EAET,gCAAK,SAAS,EAAC,6CAA6C,YAC1D,gCAAK,SAAS,EAAC,uBAAuB,YACnC,IAAI,IAAI,EAAE,CAAC,kBAAkB,CAAC,OAAO,EAAE;4CACtC,OAAO,EAAE,MAAM;4CACf,IAAI,EAAE,SAAS;4CACf,KAAK,EAAE,MAAM;4CACb,GAAG,EAAE,SAAS;yCACf,CAAC,GACE,GACF,IACF,GACC,EAGT,iCAAM,SAAS,EAAC,sDAAsD,YACpE,gCAAK,SAAS,EAAC,6BAA6B,YACzC,QAAQ,GACL,GACD,IACH,EAGL,WAAW,IAAI,CACd,gCACE,SAAS,EAAC,qDAAqD,EAC/D,OAAO,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,GACpC,CACH,IACG,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,kBAAe,MAAM,CAAC"}