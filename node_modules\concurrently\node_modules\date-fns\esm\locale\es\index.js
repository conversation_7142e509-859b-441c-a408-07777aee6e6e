import formatDistance from "./_lib/formatDistance/index.js";
import formatLong from "./_lib/formatLong/index.js";
import formatRelative from "./_lib/formatRelative/index.js";
import localize from "./_lib/localize/index.js";
import match from "./_lib/match/index.js";
/**
 * @type {Locale}
 * @category Locales
 * @summary Spanish locale.
 * @language Spanish
 * @iso-639-2 spa
 * <AUTHOR> [@juanangosto]{@link https://github.com/juanangosto}
 * <AUTHOR> [@guigrpa]{@link https://github.com/guigrpa}
 * <AUTHOR> [@fjaguero]{@link https://github.com/fjaguero}
 * <AUTHOR> [@harogaston]{@link https://github.com/harogaston}
 * <AUTHOR> [@YagoCarballo]{@link https://github.com/YagoCarballo}
 */
var locale = {
  code: 'es',
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1
  }
};
export default locale;