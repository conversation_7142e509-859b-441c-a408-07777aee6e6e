import formatDistance from "./_lib/formatDistance/index.js";
import formatLong from "./_lib/formatLong/index.js";
import formatRelative from "./_lib/formatRelative/index.js";
import localize from "./_lib/localize/index.js";
import match from "./_lib/match/index.js";
/**
 * @type {Locale}
 * @category Locales
 * @summary Dutch locale.
 * @language Dutch
 * @iso-639-2 nld
 * <AUTHOR> [@jtangelder]{@link https://github.com/jtangelder}
 * <AUTHOR> [@rubenstolk]{@link https://github.com/rubenstolk}
 * <AUTHOR> [@bitcrumb]{@link https://github.com/bitcrumb}
 * <AUTHOR> Rivai [@edorivai]{@link https://github.com/edorivai}
 * <AUTHOR> [@curry684]{@link https://github.com/curry684}
 * <AUTHOR> [@stefanvermaas]{@link https://github.com/stefanvermaas}
 */
var locale = {
  code: 'nl',
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4
  }
};
export default locale;