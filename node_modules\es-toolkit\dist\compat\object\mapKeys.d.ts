import { ListIteratee } from '../_internal/ListIteratee.js';
import { ObjectIteratee } from '../_internal/ObjectIteratee.js';

/**
 * Creates an object with the same values as `object` and keys generated by running each own enumerable string keyed property through `iteratee`.
 *
 * @template T
 * @param {ArrayLike<T> | null | undefined} object - The object to iterate over.
 * @param {ValueIteratee<T>} [iteratee] - The function invoked per iteration.
 * @returns {Record<string, T>} - Returns the new mapped object.
 *
 * @example
 * mapKeys([1, 2, 3], (value, index) => `key${index}`);
 * // => { 'key0': 1, 'key1': 2, 'key2': 3 }
 */
declare function mapKeys<T>(object: ArrayLike<T> | null | undefined, iteratee?: ListIteratee<T>): Record<string, T>;
/**
 * Creates an object with the same values as `object` and keys generated by running each own enumerable string keyed property through `iteratee`.
 *
 * @template T
 * @param {T | null | undefined} object - The object to iterate over.
 * @param {ValueIteratee<T[keyof T]>} [iteratee] - The function invoked per iteration.
 * @returns {Record<string, T[keyof T]>} - Returns the new mapped object.
 *
 * @example
 * mapKeys({ a: 1, b: 2 }, (value, key) => key + value);
 * // => { 'a1': 1, 'b2': 2 }
 */
declare function mapKeys<T extends object>(object: T | null | undefined, iteratee?: ObjectIteratee<T>): Record<string, T[keyof T]>;

export { mapKeys };
