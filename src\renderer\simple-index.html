<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الجيم</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 30px;
            font-weight: bold;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        
        .feature-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .feature h3 {
            color: #495057;
            margin-bottom: 5px;
            font-size: 16px;
        }
        
        .feature p {
            color: #6c757d;
            font-size: 14px;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .btn-info {
            background: #007bff;
            color: white;
        }
        
        .btn-info:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 12px;
        }
        
        .developer-info {
            margin-top: 15px;
        }
        
        .developer-info strong {
            color: #495057;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .container {
            animation: fadeIn 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🏋️</div>
        <h1>نظام إدارة الجيم</h1>
        <p class="subtitle">نظام شامل لإدارة النادي الرياضي</p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">👥</div>
                <h3>إدارة الأعضاء</h3>
                <p>تسجيل ومتابعة بيانات الأعضاء</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">💳</div>
                <h3>الاشتراكات</h3>
                <p>إدارة الباقات والتجديدات</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📅</div>
                <h3>الحضور</h3>
                <p>تسجيل ومتابعة الحضور</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">💰</div>
                <h3>المالية</h3>
                <p>إدارة الإيرادات والمصروفات</p>
            </div>
        </div>
        
        <div class="buttons">
            <button class="btn btn-primary" onclick="alert('سيتم تطوير هذه الميزة قريباً')">
                🚀 بدء الاستخدام
            </button>
            
            <a href="https://wa.me/966225396729" class="btn btn-success" target="_blank">
                💬 الدعم الفني
            </a>
            
            <a href="https://web.facebook.com/profile.php?id=61578888731370" class="btn btn-info" target="_blank">
                📘 متابعة صفحتنا
            </a>
        </div>
        
        <div class="footer">
            <div>نظام إدارة الجيم - الإصدار 1.0.0</div>
            <div class="developer-info">
                <strong>المطور:</strong> Eng/ Hossam Osama<br>
                <strong>تطوير:</strong> H - TECH
            </div>
            <div style="margin-top: 10px;">جميع الحقوق محفوظة © 2024</div>
        </div>
    </div>

    <script>
        // منع النقر بالزر الأيمن
        document.addEventListener('contextmenu', e => e.preventDefault());
        
        // رسالة ترحيب
        setTimeout(() => {
            console.log('مرحباً بك في نظام إدارة الجيم!');
        }, 1000);
    </script>
</body>
</html>
