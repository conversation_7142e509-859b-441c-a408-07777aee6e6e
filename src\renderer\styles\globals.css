@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* خطوط عربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  direction: rtl;
}

body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8fafc;
  color: #1e293b;
  line-height: 1.6;
}

/* تخصيص الألوان الأساسية */
:root {
  --primary-color: #3b82f6;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --light-bg: #f8fafc;
  --dark-bg: #1e293b;
  --border-color: #e2e8f0;
}

/* تخصيص الأزرار */
.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-warning {
  @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-outline {
  @apply border-2 bg-transparent hover:bg-gray-50;
}

/* تخصيص البطاقات */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.card-header {
  @apply border-b border-gray-200 pb-4 mb-4;
}

.card-title {
  @apply text-lg font-semibold text-gray-900;
}

/* تخصيص النماذج */
.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white;
}

.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical;
}

/* تخصيص الجداول */
.table {
  @apply w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm;
}

.table th {
  @apply bg-gray-50 px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200;
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200;
}

.table tbody tr:hover {
  @apply bg-gray-50;
}

/* تخصيص الشريط الجانبي */
.sidebar {
  @apply bg-white shadow-lg border-l border-gray-200;
}

.sidebar-item {
  @apply flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 hover:text-blue-600 transition-colors duration-200 cursor-pointer;
}

.sidebar-item.active {
  @apply bg-blue-50 text-blue-600 border-l-4 border-blue-600;
}

/* تخصيص الإشعارات */
.notification {
  @apply fixed top-4 left-4 right-4 z-50 p-4 rounded-lg shadow-lg;
}

.notification-success {
  @apply bg-green-100 border border-green-400 text-green-700;
}

.notification-error {
  @apply bg-red-100 border border-red-400 text-red-700;
}

.notification-warning {
  @apply bg-yellow-100 border border-yellow-400 text-yellow-700;
}

.notification-info {
  @apply bg-blue-100 border border-blue-400 text-blue-700;
}

/* تخصيص المودال */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6;
}

/* تخصيص التحميل */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

/* تخصيص الإحصائيات */
.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center;
}

.stat-number {
  @apply text-3xl font-bold text-gray-900;
}

.stat-label {
  @apply text-sm text-gray-600 mt-2;
}

/* تخصيص الرسوم البيانية */
.chart-container {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

/* تخصيص الشارات */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}

/* تخصيص التمرير */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* تحسينات للطباعة */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-after: always;
  }
}
