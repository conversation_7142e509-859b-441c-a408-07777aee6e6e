{"version": 3, "file": "Settings.js", "sourceRoot": "", "sources": ["../../../src/renderer/pages/Settings.tsx"], "names": [], "mappings": ";;;AAAA,iCAAwC;AACxC,+CAA6F;AAE7F,MAAM,QAAQ,GAAa,GAAG,EAAE;IAC9B,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,IAAA,gBAAQ,EAAC;QAC7C,OAAO,EAAE,sBAAsB;QAC/B,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,EAAE;QACZ,QAAQ,EAAE,EAAE;QACZ,QAAQ,EAAE,MAAM;KACjB,CAAC,CAAC;IAEH,MAAM,kBAAkB,GAAG,GAAG,EAAE;QAC9B,oCAAoC;QACpC,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAClC,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,GAAG,EAAE;QACxB,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC;IACtD,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,GAAG,EAAE;QACxB,MAAM,CAAC,IAAI,CAAC,wDAAwD,EAAE,QAAQ,CAAC,CAAC;IAClF,CAAC,CAAC;IAEF,OAAO,CACL,iCAAK,SAAS,EAAC,WAAW,aAExB,gCAAK,SAAS,EAAC,mCAAmC,YAChD,+BAAI,SAAS,EAAC,kCAAkC,uEAAe,GAC3D,EAEN,iCAAK,SAAS,EAAC,uCAAuC,aAEpD,iCAAK,SAAS,EAAC,0DAA0D,aACvE,gCAAK,SAAS,EAAC,aAAa,YAC1B,gCAAI,SAAS,EAAC,8BAA8B,aAC1C,uBAAC,uBAAY,IAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAC,MAAM,GAAG,iFAExC,GACD,EAEN,iCAAK,SAAS,EAAC,WAAW,aACxB,iCAAK,SAAS,EAAC,YAAY,aACzB,kCAAO,SAAS,EAAC,YAAY,kEAAkB,EAC/C,kCACE,IAAI,EAAC,MAAM,EACX,KAAK,EAAE,WAAW,CAAC,OAAO,EAC1B,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,EAAC,GAAG,WAAW,EAAE,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,EAAC,CAAC,EAC1E,SAAS,EAAC,YAAY,GACtB,IACE,EAEN,iCAAK,SAAS,EAAC,YAAY,aACzB,kCAAO,SAAS,EAAC,YAAY,2DAAgB,EAC7C,kCACE,IAAI,EAAC,MAAM,EACX,KAAK,EAAE,WAAW,CAAC,UAAU,EAC7B,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,EAAC,GAAG,WAAW,EAAE,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,EAAC,CAAC,EAC7E,SAAS,EAAC,YAAY,GACtB,IACE,EAEN,iCAAK,SAAS,EAAC,YAAY,aACzB,kCAAO,SAAS,EAAC,YAAY,wEAAmB,EAChD,kCACE,IAAI,EAAC,MAAM,EACX,KAAK,EAAE,WAAW,CAAC,QAAQ,EAC3B,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,EAAC,GAAG,WAAW,EAAE,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,EAAC,CAAC,EAC3E,SAAS,EAAC,YAAY,GACtB,IACE,EAEN,iCAAK,SAAS,EAAC,YAAY,aACzB,kCAAO,SAAS,EAAC,YAAY,kHAA0B,EACvD,kCACE,IAAI,EAAC,OAAO,EACZ,KAAK,EAAE,WAAW,CAAC,QAAQ,EAC3B,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,EAAC,GAAG,WAAW,EAAE,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,EAAC,CAAC,EAC3E,SAAS,EAAC,YAAY,GACtB,IACE,EAEN,iCAAK,SAAS,EAAC,YAAY,aACzB,kCAAO,SAAS,EAAC,YAAY,qDAAe,EAC5C,oCACE,KAAK,EAAE,WAAW,CAAC,QAAQ,EAC3B,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,EAAC,GAAG,WAAW,EAAE,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,EAAC,CAAC,EAC3E,SAAS,EAAC,aAAa,aAEvB,mCAAQ,KAAK,EAAC,0BAAM,wEAAoB,EACxC,mCAAQ,KAAK,EAAC,0BAAM,oFAAsB,EAC1C,mCAAQ,KAAK,EAAC,gCAAO,8EAAqB,EAC1C,mCAAQ,KAAK,EAAC,0BAAM,kEAAmB,IAChC,IACL,EAEN,oCACE,OAAO,EAAE,kBAAkB,EAC3B,SAAS,EAAC,yDAAyD,aAEnE,uBAAC,mBAAI,IAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAC,MAAM,GAAG,iFAE5B,IACL,IACF,EAGN,iCAAK,SAAS,EAAC,WAAW,aAExB,iCAAK,SAAS,EAAC,0DAA0D,aACvE,gCAAK,SAAS,EAAC,aAAa,YAC1B,+BAAI,SAAS,EAAC,YAAY,gGAAoB,GAC1C,EAEN,iCAAK,SAAS,EAAC,WAAW,aACxB,oCACE,OAAO,EAAE,YAAY,EACrB,SAAS,EAAC,oJAAoJ,aAE9J,uBAAC,4BAAa,IAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAC,MAAM,GAAG,qEAErC,EAET,oCACE,OAAO,EAAE,YAAY,EACrB,SAAS,EAAC,kJAAkJ,aAE5J,uBAAC,uBAAQ,IAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAC,MAAM,GAAG,iFAEhC,IACL,IACF,EAGN,iCAAK,SAAS,EAAC,0DAA0D,aACvE,gCAAK,SAAS,EAAC,aAAa,YAC1B,gCAAI,SAAS,EAAC,8BAA8B,aAC1C,uBAAC,mBAAI,IAAC,IAAI,EAAE,EAAE,EAAE,SAAS,EAAC,MAAM,GAAG,qEAEhC,GACD,EAEN,iCAAK,SAAS,EAAC,uBAAuB,aACpC,4CACE,+BAAI,SAAS,EAAC,qCAAqC,uGAAsB,EACzE,8BAAG,SAAS,EAAC,eAAe,iEAAkB,IAC1C,EAEN,iCAAK,SAAS,EAAC,+BAA+B,aAC5C,8BAAG,SAAS,EAAC,4BAA4B,sDAAY,EACrD,8BAAG,SAAS,EAAC,2BAA2B,kCAAsB,IAC1D,EAEN,iCAAK,SAAS,EAAC,+BAA+B,aAC5C,8BAAG,SAAS,EAAC,4BAA4B,gDAAW,EACpD,8BAAG,SAAS,EAAC,iCAAiC,yBAAa,IACvD,EAEN,gCAAK,SAAS,EAAC,+BAA+B,YAC5C,8BAAG,SAAS,EAAC,uBAAuB,+HAEhC,GACA,IACF,IACF,EAGN,iCAAK,SAAS,EAAC,0DAA0D,aACvE,gCAAK,SAAS,EAAC,aAAa,YAC1B,+BAAI,SAAS,EAAC,YAAY,sGAAqB,GAC3C,EAEN,iCAAK,SAAS,EAAC,WAAW,aACxB,mCAAQ,SAAS,EAAC,0BAA0B,yHAEnC,EAET,mCAAQ,SAAS,EAAC,sDAAsD,qIAE/D,IACL,IACF,IACF,IACF,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,kBAAe,QAAQ,CAAC"}