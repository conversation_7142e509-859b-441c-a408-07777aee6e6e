export interface Member {
    id?: number;
    name: string;
    phone: string;
    email?: string;
    birthDate?: string;
    height?: number;
    weight?: number;
    healthNotes?: string;
    photo?: string;
    joinDate: string;
    isActive: boolean;
    createdAt?: string;
    updatedAt?: string;
}
export interface Subscription {
    id?: number;
    memberId: number;
    packageType: string;
    price: number;
    startDate: string;
    endDate: string;
    isActive: boolean;
    createdAt?: string;
}
export interface Attendance {
    id?: number;
    memberId: number;
    checkInTime: string;
    checkOutTime?: string;
    date: string;
    createdAt?: string;
}
export interface Trainer {
    id?: number;
    name: string;
    phone: string;
    email?: string;
    specialization: string;
    salary: number;
    salaryType: 'fixed' | 'percentage';
    isActive: boolean;
    createdAt?: string;
}
export interface Equipment {
    id?: number;
    name: string;
    category: string;
    purchaseDate: string;
    price: number;
    condition: 'excellent' | 'good' | 'fair' | 'poor';
    maintenanceDate?: string;
    nextMaintenanceDate?: string;
    notes?: string;
    createdAt?: string;
}
export interface FinancialRecord {
    id?: number;
    type: 'income' | 'expense';
    category: string;
    amount: number;
    description: string;
    date: string;
    createdAt?: string;
}
export interface Product {
    id?: number;
    name: string;
    category: string;
    price: number;
    cost: number;
    stock: number;
    minStock: number;
    isActive: boolean;
    createdAt?: string;
}
export interface Sale {
    id?: number;
    productId: number;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    date: string;
    createdAt?: string;
}
export declare class DatabaseManager {
    private db;
    private dbPath;
    constructor();
    initialize(): Promise<void>;
    private createTables;
    getMembers(): Promise<Member[]>;
    getMemberById(id: number): Promise<Member | null>;
    addMember(member: Omit<Member, 'id' | 'createdAt' | 'updatedAt'>): Promise<Member>;
    updateMember(id: number, member: Partial<Member>): Promise<Member>;
    deleteMember(id: number): Promise<boolean>;
    getSubscriptions(): Promise<Subscription[]>;
    addSubscription(subscription: Omit<Subscription, 'id' | 'createdAt'>): Promise<Subscription>;
    recordAttendance(memberId: number): Promise<Attendance>;
    getAttendance(startDate?: string, endDate?: string): Promise<Attendance[]>;
    getFinancialRecords(): Promise<FinancialRecord[]>;
    addFinancialRecord(record: Omit<FinancialRecord, 'id' | 'createdAt'>): Promise<FinancialRecord>;
    getProducts(): Promise<Product[]>;
    addProduct(product: Omit<Product, 'id' | 'createdAt'>): Promise<Product>;
    updateProduct(id: number, product: Partial<Product>): Promise<Product>;
    deleteProduct(id: number): Promise<boolean>;
    getSettings(): Promise<any>;
    updateSettings(settings: any): Promise<boolean>;
    close(): Promise<void>;
}
