import { app, BrowserWindow, ipc<PERSON><PERSON>, Menu } from 'electron';
import * as path from 'path';
import { DatabaseManager } from '../database/DatabaseManager';

class GymApp {
  private mainWindow: BrowserWindow | null = null;
  private dbManager: DatabaseManager;

  constructor() {
    this.dbManager = new DatabaseManager();
  }

  private createWindow(): void {
    // إنشاء النافذة الرئيسية
    this.mainWindow = new BrowserWindow({
      height: 800,
      width: 1200,
      minHeight: 600,
      minWidth: 800,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
      },
      icon: path.join(__dirname, '../../assets/icon.png'),
      titleBarStyle: 'default',
      show: false,
    });

    // تحميل التطبيق
    const isDev = process.argv.includes('--dev');
    if (isDev) {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    // إظهار النافذة عند الانتهاء من التحميل
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
    });

    // إغلاق التطبيق عند إغلاق النافذة الرئيسية
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
  }

  private setupMenu(): void {
    const template: Electron.MenuItemConstructorOptions[] = [
      {
        label: 'ملف',
        submenu: [
          {
            label: 'نسخة احتياطية',
            accelerator: 'CmdOrCtrl+B',
            click: () => {
              this.mainWindow?.webContents.send('backup-database');
            }
          },
          {
            label: 'استيراد',
            accelerator: 'CmdOrCtrl+I',
            click: () => {
              this.mainWindow?.webContents.send('import-database');
            }
          },
          { type: 'separator' },
          {
            label: 'خروج',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => {
              app.quit();
            }
          }
        ]
      },
      {
        label: 'عرض',
        submenu: [
          { role: 'reload', label: 'إعادة تحميل' },
          { role: 'forceReload', label: 'إعادة تحميل قسري' },
          { role: 'toggleDevTools', label: 'أدوات المطور' },
          { type: 'separator' },
          { role: 'resetZoom', label: 'إعادة تعيين التكبير' },
          { role: 'zoomIn', label: 'تكبير' },
          { role: 'zoomOut', label: 'تصغير' },
          { type: 'separator' },
          { role: 'togglefullscreen', label: 'ملء الشاشة' }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  private setupIpcHandlers(): void {
    // معالجات قاعدة البيانات
    ipcMain.handle('db-get-members', async () => {
      return await this.dbManager.getMembers();
    });

    ipcMain.handle('db-add-member', async (_, memberData) => {
      return await this.dbManager.addMember(memberData);
    });

    ipcMain.handle('db-update-member', async (_, id, memberData) => {
      return await this.dbManager.updateMember(id, memberData);
    });

    ipcMain.handle('db-delete-member', async (_, id) => {
      return await this.dbManager.deleteMember(id);
    });

    // معالجات الاشتراكات
    ipcMain.handle('db-get-subscriptions', async () => {
      return await this.dbManager.getSubscriptions();
    });

    ipcMain.handle('db-add-subscription', async (_, subscriptionData) => {
      return await this.dbManager.addSubscription(subscriptionData);
    });

    // معالجات الحضور
    ipcMain.handle('db-record-attendance', async (_, memberId) => {
      return await this.dbManager.recordAttendance(memberId);
    });

    ipcMain.handle('db-get-attendance', async (_, startDate, endDate) => {
      return await this.dbManager.getAttendance(startDate, endDate);
    });
  }

  public async initialize(): Promise<void> {
    // انتظار جاهزية Electron
    await app.whenReady();

    // إنشاء قاعدة البيانات
    await this.dbManager.initialize();

    // إعداد التطبيق
    this.createWindow();
    this.setupMenu();
    this.setupIpcHandlers();

    // معالجة إعادة تفعيل التطبيق على macOS
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      }
    });

    // إغلاق التطبيق عند إغلاق جميع النوافذ
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });
  }
}

// تشغيل التطبيق
const gymApp = new GymApp();
gymApp.initialize().catch(console.error);
