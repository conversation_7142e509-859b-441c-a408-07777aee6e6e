{"name": "gym-management-system", "version": "1.0.0", "description": "نظام إدارة جيم شامل لشخص واحد", "main": "dist/main.js", "scripts": {"start": "electron dist/main.js", "dev": "concurrently \"npm run build:watch\" \"npm run electron:dev\"", "build": "tsc && npm run build:renderer", "build:watch": "tsc --watch", "build:renderer": "webpack --mode production", "build:renderer:dev": "webpack --mode development --watch", "electron:dev": "wait-on dist/main.js && electron dist/main.js --dev", "pack": "electron-builder --dir", "dist": "npm run build && electron-builder", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.gym.management", "productName": "نظام إدارة الجيم", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}, "keywords": ["gym", "management", "electron", "react", "typescript"], "author": "Gym Management System", "license": "MIT", "type": "commonjs", "devDependencies": {"concurrently": "^8.2.2", "wait-on": "^7.2.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "html-webpack-plugin": "^5.6.0", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "ts-loader": "^9.5.1"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@types/better-sqlite3": "^7.6.13", "@types/node": "^24.3.0", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@types/react-router-dom": "^5.3.3", "better-sqlite3": "^12.2.0", "date-fns": "^4.1.0", "electron": "^37.4.0", "electron-builder": "^26.0.12", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.2", "recharts": "^3.1.2", "sqlite3": "^5.1.7", "tailwindcss": "^4.1.12", "typescript": "^5.9.2"}}