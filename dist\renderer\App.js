"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const jsx_runtime_1 = require("react/jsx-runtime");
const react_router_dom_1 = require("react-router-dom");
const Layout_1 = __importDefault(require("./components/Layout"));
const Dashboard_1 = __importDefault(require("./pages/Dashboard"));
const Members_1 = __importDefault(require("./pages/Members"));
const Subscriptions_1 = __importDefault(require("./pages/Subscriptions"));
const Attendance_1 = __importDefault(require("./pages/Attendance"));
const Trainers_1 = __importDefault(require("./pages/Trainers"));
const Equipment_1 = __importDefault(require("./pages/Equipment"));
const Finance_1 = __importDefault(require("./pages/Finance"));
const Shop_1 = __importDefault(require("./pages/Shop"));
const Settings_1 = __importDefault(require("./pages/Settings"));
const App = () => {
    return ((0, jsx_runtime_1.jsx)(Layout_1.default, { children: (0, jsx_runtime_1.jsxs)(react_router_dom_1.Routes, { children: [(0, jsx_runtime_1.jsx)(react_router_dom_1.Route, { path: "/", element: (0, jsx_runtime_1.jsx)(Dashboard_1.default, {}) }), (0, jsx_runtime_1.jsx)(react_router_dom_1.Route, { path: "/members", element: (0, jsx_runtime_1.jsx)(Members_1.default, {}) }), (0, jsx_runtime_1.jsx)(react_router_dom_1.Route, { path: "/subscriptions", element: (0, jsx_runtime_1.jsx)(Subscriptions_1.default, {}) }), (0, jsx_runtime_1.jsx)(react_router_dom_1.Route, { path: "/attendance", element: (0, jsx_runtime_1.jsx)(Attendance_1.default, {}) }), (0, jsx_runtime_1.jsx)(react_router_dom_1.Route, { path: "/trainers", element: (0, jsx_runtime_1.jsx)(Trainers_1.default, {}) }), (0, jsx_runtime_1.jsx)(react_router_dom_1.Route, { path: "/equipment", element: (0, jsx_runtime_1.jsx)(Equipment_1.default, {}) }), (0, jsx_runtime_1.jsx)(react_router_dom_1.Route, { path: "/finance", element: (0, jsx_runtime_1.jsx)(Finance_1.default, {}) }), (0, jsx_runtime_1.jsx)(react_router_dom_1.Route, { path: "/shop", element: (0, jsx_runtime_1.jsx)(Shop_1.default, {}) }), (0, jsx_runtime_1.jsx)(react_router_dom_1.Route, { path: "/settings", element: (0, jsx_runtime_1.jsx)(Settings_1.default, {}) })] }) }));
};
exports.default = App;
//# sourceMappingURL=App.js.map