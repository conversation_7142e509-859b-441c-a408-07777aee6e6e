import * as React from 'react';
import { CSSProperties } from 'react';
import { AnimationManager, ReactSmoothStyle } from './AnimationManager';
type CSSTransitionAnimateProps<T extends ReactSmoothStyle> = {
    animationManager?: AnimationManager;
    duration?: number;
    begin?: number;
    easing?: string;
    isActive?: boolean;
    canBegin?: boolean;
    from: T;
    to: T;
    attributeName: string;
    onAnimationStart?: () => void;
    onAnimationEnd?: () => void;
    children: (style: CSSProperties) => React.ReactNode;
};
export declare function CSSTransitionAnimate<T extends ReactSmoothStyle>(outsideProps: CSSTransitionAnimateProps<T>): React.ReactNode;
export {};
