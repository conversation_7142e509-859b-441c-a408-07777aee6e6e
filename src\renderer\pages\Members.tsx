import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, Eye, Phone, Mail, Users } from 'lucide-react';

interface Member {
  id: number;
  name: string;
  phone: string;
  email?: string;
  birthDate?: string;
  height?: number;
  weight?: number;
  healthNotes?: string;
  photo?: string;
  joinDate: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

const Members: React.FC = () => {
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);

  useEffect(() => {
    loadMembers();
  }, []);

  const loadMembers = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل البيانات - سيتم استبدالها بالاستدعاء الفعلي
      setTimeout(() => {
        const mockMembers: Member[] = [
          {
            id: 1,
            name: 'أحمد محمد علي',
            phone: '**********',
            email: '<EMAIL>',
            birthDate: '1990-05-15',
            height: 175,
            weight: 80,
            joinDate: '2024-01-15',
            isActive: true
          },
          {
            id: 2,
            name: 'سارة أحمد',
            phone: '0507654321',
            email: '<EMAIL>',
            birthDate: '1995-08-22',
            height: 165,
            weight: 60,
            joinDate: '2024-02-01',
            isActive: true
          },
          {
            id: 3,
            name: 'محمد عبدالله',
            phone: '0509876543',
            birthDate: '1988-12-10',
            height: 180,
            weight: 85,
            joinDate: '2024-01-20',
            isActive: false
          }
        ];
        setMembers(mockMembers);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error loading members:', error);
      setLoading(false);
    }
  };

  const filteredMembers = members.filter(member =>
    member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.phone.includes(searchTerm)
  );

  const handleAddMember = () => {
    setShowAddModal(true);
  };

  const handleEditMember = (member: Member) => {
    console.log('Edit member:', member);
  };

  const handleDeleteMember = (member: Member) => {
    if (confirm(`هل أنت متأكد من حذف العضو ${member.name}؟`)) {
      setMembers(members.filter(m => m.id !== member.id));
    }
  };

  const handleViewMember = (member: Member) => {
    console.log('View member:', member);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner"></div>
        <span className="mr-3 text-gray-600">جاري تحميل الأعضاء...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">إدارة الأعضاء</h1>
        <button
          onClick={handleAddMember}
          className="btn btn-primary flex items-center"
        >
          <Plus size={20} className="ml-2" />
          إضافة عضو جديد
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search size={20} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="البحث بالاسم أو رقم الهاتف..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pr-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select className="form-select">
              <option value="">جميع الأعضاء</option>
              <option value="active">الأعضاء النشطون</option>
              <option value="inactive">الأعضاء غير النشطين</option>
            </select>
            <button className="btn btn-secondary">تصفية</button>
          </div>
        </div>
      </div>

      {/* Members Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="stat-card">
          <div className="stat-number text-blue-600">{members.length}</div>
          <div className="stat-label">إجمالي الأعضاء</div>
        </div>
        <div className="stat-card">
          <div className="stat-number text-green-600">{members.filter(m => m.isActive).length}</div>
          <div className="stat-label">الأعضاء النشطون</div>
        </div>
        <div className="stat-card">
          <div className="stat-number text-red-600">{members.filter(m => !m.isActive).length}</div>
          <div className="stat-label">الأعضاء غير النشطين</div>
        </div>
      </div>

      {/* Members Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="table">
            <thead>
              <tr>
                <th>الاسم</th>
                <th>رقم الهاتف</th>
                <th>البريد الإلكتروني</th>
                <th>تاريخ الانضمام</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredMembers.map((member) => (
                <tr key={member.id}>
                  <td>
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                        <span className="text-blue-600 font-medium">
                          {member.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{member.name}</div>
                        {member.birthDate && (
                          <div className="text-sm text-gray-500">
                            {new Date().getFullYear() - new Date(member.birthDate).getFullYear()} سنة
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      <Phone size={16} className="text-gray-400 ml-2" />
                      {member.phone}
                    </div>
                  </td>
                  <td>
                    {member.email ? (
                      <div className="flex items-center">
                        <Mail size={16} className="text-gray-400 ml-2" />
                        {member.email}
                      </div>
                    ) : (
                      <span className="text-gray-400">غير محدد</span>
                    )}
                  </td>
                  <td>{new Date(member.joinDate).toLocaleDateString('ar-SA')}</td>
                  <td>
                    <span className={`badge ${member.isActive ? 'badge-success' : 'badge-danger'}`}>
                      {member.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleViewMember(member)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
                        title="عرض التفاصيل"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => handleEditMember(member)}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg"
                        title="تعديل"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteMember(member)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                        title="حذف"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredMembers.length === 0 && (
          <div className="text-center py-12">
            <Users size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد أعضاء</h3>
            <p className="text-gray-500 mb-4">ابدأ بإضافة أول عضو في النادي</p>
            <button
              onClick={handleAddMember}
              className="btn btn-primary"
            >
              إضافة عضو جديد
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Members;
