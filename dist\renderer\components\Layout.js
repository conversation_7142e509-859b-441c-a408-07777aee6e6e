"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const react_router_dom_1 = require("react-router-dom");
const lucide_react_1 = require("lucide-react");
const Layout = ({ children }) => {
    const [sidebarOpen, setSidebarOpen] = (0, react_1.useState)(false);
    const location = (0, react_router_dom_1.useLocation)();
    const menuItems = [
        { path: '/', icon: lucide_react_1.Home, label: 'لوحة التحكم' },
        { path: '/members', icon: lucide_react_1.Users, label: 'الأعضاء' },
        { path: '/subscriptions', icon: lucide_react_1.CreditCard, label: 'الاشتراكات' },
        { path: '/attendance', icon: lucide_react_1.Calendar, label: 'الحضور' },
        { path: '/trainers', icon: lucide_react_1.User<PERSON><PERSON>ck, label: 'المدربين' },
        { path: '/equipment', icon: lucide_react_1.Dumbbell, label: 'الأجهزة' },
        { path: '/finance', icon: lucide_react_1.DollarSign, label: 'المالية' },
        { path: '/shop', icon: lucide_react_1.ShoppingCart, label: 'المتجر' },
        { path: '/settings', icon: lucide_react_1.Settings, label: 'الإعدادات' },
    ];
    const isActive = (path) => {
        return location.pathname === path;
    };
    return ((0, jsx_runtime_1.jsxs)("div", { className: "flex h-screen bg-gray-100", children: [(0, jsx_runtime_1.jsxs)("div", { className: `fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform ${sidebarOpen ? 'translate-x-0' : 'translate-x-full'} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`, children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-between h-16 px-6 bg-blue-600 text-white", children: [(0, jsx_runtime_1.jsx)("h1", { className: "text-xl font-bold", children: "\u0646\u0638\u0627\u0645 \u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u062C\u064A\u0645" }), (0, jsx_runtime_1.jsx)("button", { onClick: () => setSidebarOpen(false), className: "lg:hidden", children: (0, jsx_runtime_1.jsx)(lucide_react_1.X, { size: 24 }) })] }), (0, jsx_runtime_1.jsx)("nav", { className: "mt-8", children: menuItems.map((item) => {
                            const Icon = item.icon;
                            return ((0, jsx_runtime_1.jsxs)(react_router_dom_1.Link, { to: item.path, className: `flex items-center px-6 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200 ${isActive(item.path) ? 'bg-blue-50 text-blue-600 border-l-4 border-blue-600' : ''}`, onClick: () => setSidebarOpen(false), children: [(0, jsx_runtime_1.jsx)(Icon, { size: 20, className: "ml-3" }), (0, jsx_runtime_1.jsx)("span", { className: "font-medium", children: item.label })] }, item.path));
                        }) }), (0, jsx_runtime_1.jsx)("div", { className: "absolute bottom-0 left-0 right-0 p-6 text-center text-sm text-gray-500", children: (0, jsx_runtime_1.jsx)("p", { children: "\u0646\u0638\u0627\u0645 \u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u062C\u064A\u0645 v1.0" }) })] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex-1 flex flex-col overflow-hidden lg:mr-64", children: [(0, jsx_runtime_1.jsx)("header", { className: "bg-white shadow-sm border-b border-gray-200", children: (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-between h-16 px-6", children: [(0, jsx_runtime_1.jsx)("button", { onClick: () => setSidebarOpen(true), className: "lg:hidden text-gray-600 hover:text-gray-900", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Menu, { size: 24 }) }), (0, jsx_runtime_1.jsx)("div", { className: "flex items-center space-x-4 space-x-reverse", children: (0, jsx_runtime_1.jsx)("div", { className: "text-sm text-gray-600", children: new Date().toLocaleDateString('ar-SA', {
                                            weekday: 'long',
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric'
                                        }) }) })] }) }), (0, jsx_runtime_1.jsx)("main", { className: "flex-1 overflow-x-hidden overflow-y-auto bg-gray-100", children: (0, jsx_runtime_1.jsx)("div", { className: "container mx-auto px-6 py-8", children: children }) })] }), sidebarOpen && ((0, jsx_runtime_1.jsx)("div", { className: "fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden", onClick: () => setSidebarOpen(false) }))] }));
};
exports.default = Layout;
//# sourceMappingURL=Layout.js.map