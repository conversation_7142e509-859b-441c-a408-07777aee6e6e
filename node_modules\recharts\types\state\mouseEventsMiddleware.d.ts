import { MousePointer } from '../util/types';
export declare const mouseClickAction: import("@reduxjs/toolkit").ActionCreatorWithOptionalPayload<MousePointer, string>;
export declare const mouseClickMiddleware: import("@reduxjs/toolkit").ListenerMiddlewareInstance<unknown, import("@reduxjs/toolkit").ThunkDispatch<unknown, unknown, import("redux").AnyAction>, unknown>;
export declare const mouseMoveAction: import("@reduxjs/toolkit").ActionCreatorWithOptionalPayload<MousePointer, string>;
export declare const mouseMoveMiddleware: import("@reduxjs/toolkit").ListenerMiddlewareInstance<unknown, import("@reduxjs/toolkit").ThunkDispatch<unknown, unknown, import("redux").AnyAction>, unknown>;
