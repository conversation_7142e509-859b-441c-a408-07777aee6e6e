import buildLocalizeFn from "../../../_lib/buildLocalizeFn/index.js";
var eraValues = {
  narrow: ['М.А', 'М'],
  abbreviated: ['М.А', 'М'],
  wide: ['Милоддан Аввалги', 'Милодий']
};
var quarterValues = {
  narrow: ['1', '2', '3', '4'],
  abbreviated: ['1-чор.', '2-чор.', '3-чор.', '4-чор.'],
  wide: ['1-чорак', '2-чорак', '3-чорак', '4-чорак']
};
var monthValues = {
  narrow: ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],
  abbreviated: ['янв', 'фев', 'мар', 'апр', 'май', 'июн', 'июл', 'авг', 'сен', 'окт', 'ноя', 'дек'],
  wide: ['январ', 'феврал', 'март', 'апрел', 'май', 'июн', 'июл', 'август', 'сентабр', 'октабр', 'ноябр', 'декабр']
};
var dayValues = {
  narrow: ['Я', 'Д', 'С', 'Ч', 'П', 'Ж', 'Ш'],
  short: ['як', 'ду', 'се', 'чо', 'па', 'жу', 'ша'],
  abbreviated: ['якш', 'душ', 'сеш', 'чор', 'пай', 'жум', 'шан'],
  wide: ['якшанба', 'душанба', 'сешанба', 'чоршанба', 'пайшанба', 'жума', 'шанба']
};
var dayPeriodValues = {
  any: {
    am: 'П.О.',
    pm: 'П.К.',
    midnight: 'ярим тун',
    noon: 'пешин',
    morning: 'эрталаб',
    afternoon: 'пешиндан кейин',
    evening: 'кечаси',
    night: 'тун'
  }
};
var formattingDayPeriodValues = {
  any: {
    am: 'П.О.',
    pm: 'П.К.',
    midnight: 'ярим тун',
    noon: 'пешин',
    morning: 'эрталаб',
    afternoon: 'пешиндан кейин',
    evening: 'кечаси',
    night: 'тун'
  }
};
var ordinalNumber = function ordinalNumber(dirtyNumber, _options) {
  return String(dirtyNumber);
};
var localize = {
  ordinalNumber: ordinalNumber,
  era: buildLocalizeFn({
    values: eraValues,
    defaultWidth: 'wide'
  }),
  quarter: buildLocalizeFn({
    values: quarterValues,
    defaultWidth: 'wide',
    argumentCallback: function argumentCallback(quarter) {
      return quarter - 1;
    }
  }),
  month: buildLocalizeFn({
    values: monthValues,
    defaultWidth: 'wide'
  }),
  day: buildLocalizeFn({
    values: dayValues,
    defaultWidth: 'wide'
  }),
  dayPeriod: buildLocalizeFn({
    values: dayPeriodValues,
    defaultWidth: 'any',
    formattingValues: formattingDayPeriodValues,
    defaultFormattingWidth: 'any'
  })
};
export default localize;