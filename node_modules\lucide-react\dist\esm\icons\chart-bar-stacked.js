/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M11 13v4", key: "vyy2rb" }],
  ["path", { d: "M15 5v4", key: "1gx88a" }],
  ["path", { d: "M3 3v16a2 2 0 0 0 2 2h16", key: "c24i48" }],
  ["rect", { x: "7", y: "13", width: "9", height: "4", rx: "1", key: "1iip1u" }],
  ["rect", { x: "7", y: "5", width: "12", height: "4", rx: "1", key: "1anskk" }]
];
const ChartBarStacked = createLucideIcon("chart-bar-stacked", __iconNode);

export { __iconNode, ChartBarStacked as default };
//# sourceMappingURL=chart-bar-stacked.js.map
