import formatDistance from "./_lib/formatDistance/index.js";
import formatLong from "./_lib/formatLong/index.js";
import formatRelative from "./_lib/formatRelative/index.js";
import localize from "./_lib/localize/index.js";
import match from "./_lib/match/index.js";
/**
 * @type {Locale}
 * @category Locales
 * @summary Japanese locale.
 * @language Japanese
 * @iso-639-2 jpn
 * <AUTHOR> [@DeMuu]{@link https://github.com/DeMuu}
 * <AUTHOR> [@ykzts]{@link https://github.com/ykzts}
 * <AUTHOR> [@mesqueeb]{@link https://github.com/mesqueeb}
 * <AUTHOR> [@skyuplam]{@link https://github.com/skyuplam}
 * <AUTHOR> [@so99ynoodles]{@link https://github.com/so99ynoodles}
 */
var locale = {
  code: 'ja',
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1
  }
};
export default locale;