/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 6h8", key: "zvc2xc" }],
  ["path", { d: "M12 16h6", key: "yi5mkt" }],
  ["path", { d: "M3 3v16a2 2 0 0 0 2 2h16", key: "c24i48" }],
  ["path", { d: "M8 11h7", key: "wz2hg0" }]
];
const ChartGantt = createLucideIcon("chart-gantt", __iconNode);

export { __iconNode, ChartGantt as default };
//# sourceMappingURL=chart-gantt.js.map
