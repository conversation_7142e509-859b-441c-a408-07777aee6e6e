"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const lucide_react_1 = require("lucide-react");
const Members = () => {
    const [members, setMembers] = (0, react_1.useState)([]);
    const [loading, setLoading] = (0, react_1.useState)(true);
    const [searchTerm, setSearchTerm] = (0, react_1.useState)('');
    const [showAddModal, setShowAddModal] = (0, react_1.useState)(false);
    (0, react_1.useEffect)(() => {
        loadMembers();
    }, []);
    const loadMembers = async () => {
        try {
            setLoading(true);
            // محاكاة تحميل البيانات - سيتم استبدالها بالاستدعاء الفعلي
            setTimeout(() => {
                const mockMembers = [
                    {
                        id: 1,
                        name: 'أحمد محمد علي',
                        phone: '0501234567',
                        email: '<EMAIL>',
                        birthDate: '1990-05-15',
                        height: 175,
                        weight: 80,
                        joinDate: '2024-01-15',
                        isActive: true
                    },
                    {
                        id: 2,
                        name: 'سارة أحمد',
                        phone: '0507654321',
                        email: '<EMAIL>',
                        birthDate: '1995-08-22',
                        height: 165,
                        weight: 60,
                        joinDate: '2024-02-01',
                        isActive: true
                    },
                    {
                        id: 3,
                        name: 'محمد عبدالله',
                        phone: '0509876543',
                        birthDate: '1988-12-10',
                        height: 180,
                        weight: 85,
                        joinDate: '2024-01-20',
                        isActive: false
                    }
                ];
                setMembers(mockMembers);
                setLoading(false);
            }, 1000);
        }
        catch (error) {
            console.error('Error loading members:', error);
            setLoading(false);
        }
    };
    const filteredMembers = members.filter(member => member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.phone.includes(searchTerm));
    const handleAddMember = () => {
        setShowAddModal(true);
    };
    const handleEditMember = (member) => {
        console.log('Edit member:', member);
    };
    const handleDeleteMember = (member) => {
        if (confirm(`هل أنت متأكد من حذف العضو ${member.name}؟`)) {
            setMembers(members.filter(m => m.id !== member.id));
        }
    };
    const handleViewMember = (member) => {
        console.log('View member:', member);
    };
    if (loading) {
        return ((0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-center h-64", children: [(0, jsx_runtime_1.jsx)("div", { className: "loading-spinner" }), (0, jsx_runtime_1.jsx)("span", { className: "mr-3 text-gray-600", children: "\u062C\u0627\u0631\u064A \u062A\u062D\u0645\u064A\u0644 \u0627\u0644\u0623\u0639\u0636\u0627\u0621..." })] }));
    }
    return ((0, jsx_runtime_1.jsxs)("div", { className: "space-y-6", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-between", children: [(0, jsx_runtime_1.jsx)("h1", { className: "text-2xl font-bold text-gray-900", children: "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0623\u0639\u0636\u0627\u0621" }), (0, jsx_runtime_1.jsxs)("button", { onClick: handleAddMember, className: "btn btn-primary flex items-center", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Plus, { size: 20, className: "ml-2" }), "\u0625\u0636\u0627\u0641\u0629 \u0639\u0636\u0648 \u062C\u062F\u064A\u062F"] })] }), (0, jsx_runtime_1.jsx)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6", children: (0, jsx_runtime_1.jsxs)("div", { className: "flex flex-col md:flex-row gap-4", children: [(0, jsx_runtime_1.jsx)("div", { className: "flex-1", children: (0, jsx_runtime_1.jsxs)("div", { className: "relative", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Search, { size: 20, className: "absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" }), (0, jsx_runtime_1.jsx)("input", { type: "text", placeholder: "\u0627\u0644\u0628\u062D\u062B \u0628\u0627\u0644\u0627\u0633\u0645 \u0623\u0648 \u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062A\u0641...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), className: "form-input pr-10" })] }) }), (0, jsx_runtime_1.jsxs)("div", { className: "flex gap-2", children: [(0, jsx_runtime_1.jsxs)("select", { className: "form-select", children: [(0, jsx_runtime_1.jsx)("option", { value: "", children: "\u062C\u0645\u064A\u0639 \u0627\u0644\u0623\u0639\u0636\u0627\u0621" }), (0, jsx_runtime_1.jsx)("option", { value: "active", children: "\u0627\u0644\u0623\u0639\u0636\u0627\u0621 \u0627\u0644\u0646\u0634\u0637\u0648\u0646" }), (0, jsx_runtime_1.jsx)("option", { value: "inactive", children: "\u0627\u0644\u0623\u0639\u0636\u0627\u0621 \u063A\u064A\u0631 \u0627\u0644\u0646\u0634\u0637\u064A\u0646" })] }), (0, jsx_runtime_1.jsx)("button", { className: "btn btn-secondary", children: "\u062A\u0635\u0641\u064A\u0629" })] })] }) }), (0, jsx_runtime_1.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-6", children: [(0, jsx_runtime_1.jsxs)("div", { className: "stat-card", children: [(0, jsx_runtime_1.jsx)("div", { className: "stat-number text-blue-600", children: members.length }), (0, jsx_runtime_1.jsx)("div", { className: "stat-label", children: "\u0625\u062C\u0645\u0627\u0644\u064A \u0627\u0644\u0623\u0639\u0636\u0627\u0621" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "stat-card", children: [(0, jsx_runtime_1.jsx)("div", { className: "stat-number text-green-600", children: members.filter(m => m.isActive).length }), (0, jsx_runtime_1.jsx)("div", { className: "stat-label", children: "\u0627\u0644\u0623\u0639\u0636\u0627\u0621 \u0627\u0644\u0646\u0634\u0637\u0648\u0646" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "stat-card", children: [(0, jsx_runtime_1.jsx)("div", { className: "stat-number text-red-600", children: members.filter(m => !m.isActive).length }), (0, jsx_runtime_1.jsx)("div", { className: "stat-label", children: "\u0627\u0644\u0623\u0639\u0636\u0627\u0621 \u063A\u064A\u0631 \u0627\u0644\u0646\u0634\u0637\u064A\u0646" })] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden", children: [(0, jsx_runtime_1.jsx)("div", { className: "overflow-x-auto", children: (0, jsx_runtime_1.jsxs)("table", { className: "table", children: [(0, jsx_runtime_1.jsx)("thead", { children: (0, jsx_runtime_1.jsxs)("tr", { children: [(0, jsx_runtime_1.jsx)("th", { children: "\u0627\u0644\u0627\u0633\u0645" }), (0, jsx_runtime_1.jsx)("th", { children: "\u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062A\u0641" }), (0, jsx_runtime_1.jsx)("th", { children: "\u0627\u0644\u0628\u0631\u064A\u062F \u0627\u0644\u0625\u0644\u0643\u062A\u0631\u0648\u0646\u064A" }), (0, jsx_runtime_1.jsx)("th", { children: "\u062A\u0627\u0631\u064A\u062E \u0627\u0644\u0627\u0646\u0636\u0645\u0627\u0645" }), (0, jsx_runtime_1.jsx)("th", { children: "\u0627\u0644\u062D\u0627\u0644\u0629" }), (0, jsx_runtime_1.jsx)("th", { children: "\u0627\u0644\u0625\u062C\u0631\u0627\u0621\u0627\u062A" })] }) }), (0, jsx_runtime_1.jsx)("tbody", { children: filteredMembers.map((member) => ((0, jsx_runtime_1.jsxs)("tr", { children: [(0, jsx_runtime_1.jsx)("td", { children: (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center ml-3", children: (0, jsx_runtime_1.jsx)("span", { className: "text-blue-600 font-medium", children: member.name.charAt(0) }) }), (0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("div", { className: "font-medium text-gray-900", children: member.name }), member.birthDate && ((0, jsx_runtime_1.jsxs)("div", { className: "text-sm text-gray-500", children: [new Date().getFullYear() - new Date(member.birthDate).getFullYear(), " \u0633\u0646\u0629"] }))] })] }) }), (0, jsx_runtime_1.jsx)("td", { children: (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Phone, { size: 16, className: "text-gray-400 ml-2" }), member.phone] }) }), (0, jsx_runtime_1.jsx)("td", { children: member.email ? ((0, jsx_runtime_1.jsxs)("div", { className: "flex items-center", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Mail, { size: 16, className: "text-gray-400 ml-2" }), member.email] })) : ((0, jsx_runtime_1.jsx)("span", { className: "text-gray-400", children: "\u063A\u064A\u0631 \u0645\u062D\u062F\u062F" })) }), (0, jsx_runtime_1.jsx)("td", { children: new Date(member.joinDate).toLocaleDateString('ar-SA') }), (0, jsx_runtime_1.jsx)("td", { children: (0, jsx_runtime_1.jsx)("span", { className: `badge ${member.isActive ? 'badge-success' : 'badge-danger'}`, children: member.isActive ? 'نشط' : 'غير نشط' }) }), (0, jsx_runtime_1.jsx)("td", { children: (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center gap-2", children: [(0, jsx_runtime_1.jsx)("button", { onClick: () => handleViewMember(member), className: "p-2 text-blue-600 hover:bg-blue-50 rounded-lg", title: "\u0639\u0631\u0636 \u0627\u0644\u062A\u0641\u0627\u0635\u064A\u0644", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Eye, { size: 16 }) }), (0, jsx_runtime_1.jsx)("button", { onClick: () => handleEditMember(member), className: "p-2 text-green-600 hover:bg-green-50 rounded-lg", title: "\u062A\u0639\u062F\u064A\u0644", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Edit, { size: 16 }) }), (0, jsx_runtime_1.jsx)("button", { onClick: () => handleDeleteMember(member), className: "p-2 text-red-600 hover:bg-red-50 rounded-lg", title: "\u062D\u0630\u0641", children: (0, jsx_runtime_1.jsx)(lucide_react_1.Trash2, { size: 16 }) })] }) })] }, member.id))) })] }) }), filteredMembers.length === 0 && ((0, jsx_runtime_1.jsxs)("div", { className: "text-center py-12", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Users, { size: 48, className: "mx-auto text-gray-400 mb-4" }), (0, jsx_runtime_1.jsx)("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: "\u0644\u0627 \u062A\u0648\u062C\u062F \u0623\u0639\u0636\u0627\u0621" }), (0, jsx_runtime_1.jsx)("p", { className: "text-gray-500 mb-4", children: "\u0627\u0628\u062F\u0623 \u0628\u0625\u0636\u0627\u0641\u0629 \u0623\u0648\u0644 \u0639\u0636\u0648 \u0641\u064A \u0627\u0644\u0646\u0627\u062F\u064A" }), (0, jsx_runtime_1.jsx)("button", { onClick: handleAddMember, className: "btn btn-primary", children: "\u0625\u0636\u0627\u0641\u0629 \u0639\u0636\u0648 \u062C\u062F\u064A\u062F" })] }))] })] }));
};
exports.default = Members;
//# sourceMappingURL=Members.js.map