"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const jsx_runtime_1 = require("react/jsx-runtime");
const lucide_react_1 = require("lucide-react");
const Shop = () => {
    return ((0, jsx_runtime_1.jsxs)("div", { className: "space-y-6", children: [(0, jsx_runtime_1.jsx)("div", { className: "flex items-center justify-between", children: (0, jsx_runtime_1.jsx)("h1", { className: "text-2xl font-bold text-gray-900", children: "\u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u0645\u062A\u062C\u0631" }) }), (0, jsx_runtime_1.jsxs)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.ShoppingCart, { size: 64, className: "mx-auto text-gray-400 mb-4" }), (0, jsx_runtime_1.jsx)("h2", { className: "text-xl font-semibold text-gray-900 mb-2", children: "\u0635\u0641\u062D\u0629 \u0627\u0644\u0645\u062A\u062C\u0631" }), (0, jsx_runtime_1.jsx)("p", { className: "text-gray-600", children: "\u0633\u064A\u062A\u0645 \u062A\u0637\u0648\u064A\u0631 \u0647\u0630\u0647 \u0627\u0644\u0635\u0641\u062D\u0629 \u0642\u0631\u064A\u0628\u0627\u064B" })] })] }));
};
exports.default = Shop;
//# sourceMappingURL=Shop.js.map