/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M12 3v17a1 1 0 0 1-1 1H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v6a1 1 0 0 1-1 1H3",
      key: "11za1p"
    }
  ],
  ["path", { d: "M16 19h6", key: "xwg31i" }],
  ["path", { d: "M19 22v-6", key: "qhmiwi" }]
];
const Grid2x2Plus = createLucideIcon("grid-2x2-plus", __iconNode);

export { __iconNode, Grid2x2Plus as default };
//# sourceMappingURL=grid-2x2-plus.js.map
