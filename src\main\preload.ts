import { contextBridge, ipcRenderer } from 'electron';

// تعريف واجهة API للتطبيق
export interface ElectronAPI {
  // عمليات الأعضاء
  getMembers: () => Promise<any[]>;
  addMember: (memberData: any) => Promise<any>;
  updateMember: (id: number, memberData: any) => Promise<any>;
  deleteMember: (id: number) => Promise<boolean>;

  // عمليات الاشتراكات
  getSubscriptions: () => Promise<any[]>;
  addSubscription: (subscriptionData: any) => Promise<any>;
  updateSubscription: (id: number, subscriptionData: any) => Promise<any>;
  deleteSubscription: (id: number) => Promise<boolean>;

  // عمليات الحضور
  recordAttendance: (memberId: number) => Promise<any>;
  getAttendance: (startDate?: string, endDate?: string) => Promise<any[]>;

  // عمليات المدربين
  getTrainers: () => Promise<any[]>;
  addTrainer: (trainerData: any) => Promise<any>;
  updateTrainer: (id: number, trainerData: any) => Promise<any>;
  deleteTrainer: (id: number) => Promise<boolean>;

  // عمليات الأجهزة
  getEquipment: () => Promise<any[]>;
  addEquipment: (equipmentData: any) => Promise<any>;
  updateEquipment: (id: number, equipmentData: any) => Promise<any>;
  deleteEquipment: (id: number) => Promise<boolean>;

  // العمليات المالية
  getFinancialRecords: () => Promise<any[]>;
  addFinancialRecord: (recordData: any) => Promise<any>;
  getFinancialSummary: (startDate?: string, endDate?: string) => Promise<any>;

  // عمليات المتجر
  getProducts: () => Promise<any[]>;
  addProduct: (productData: any) => Promise<any>;
  updateProduct: (id: number, productData: any) => Promise<any>;
  deleteProduct: (id: number) => Promise<boolean>;
  recordSale: (saleData: any) => Promise<any>;

  // عمليات النظام
  backupDatabase: () => Promise<boolean>;
  importDatabase: (filePath: string) => Promise<boolean>;
  getSettings: () => Promise<any>;
  updateSettings: (settings: any) => Promise<boolean>;

  // مستمعي الأحداث
  onBackupDatabase: (callback: () => void) => void;
  onImportDatabase: (callback: () => void) => void;
}

// تعريف API للنافذة
const electronAPI: ElectronAPI = {
  // عمليات الأعضاء
  getMembers: () => ipcRenderer.invoke('db-get-members'),
  addMember: (memberData) => ipcRenderer.invoke('db-add-member', memberData),
  updateMember: (id, memberData) => ipcRenderer.invoke('db-update-member', id, memberData),
  deleteMember: (id) => ipcRenderer.invoke('db-delete-member', id),

  // عمليات الاشتراكات
  getSubscriptions: () => ipcRenderer.invoke('db-get-subscriptions'),
  addSubscription: (subscriptionData) => ipcRenderer.invoke('db-add-subscription', subscriptionData),
  updateSubscription: (id, subscriptionData) => ipcRenderer.invoke('db-update-subscription', id, subscriptionData),
  deleteSubscription: (id) => ipcRenderer.invoke('db-delete-subscription', id),

  // عمليات الحضور
  recordAttendance: (memberId) => ipcRenderer.invoke('db-record-attendance', memberId),
  getAttendance: (startDate, endDate) => ipcRenderer.invoke('db-get-attendance', startDate, endDate),

  // عمليات المدربين
  getTrainers: () => ipcRenderer.invoke('db-get-trainers'),
  addTrainer: (trainerData) => ipcRenderer.invoke('db-add-trainer', trainerData),
  updateTrainer: (id, trainerData) => ipcRenderer.invoke('db-update-trainer', id, trainerData),
  deleteTrainer: (id) => ipcRenderer.invoke('db-delete-trainer', id),

  // عمليات الأجهزة
  getEquipment: () => ipcRenderer.invoke('db-get-equipment'),
  addEquipment: (equipmentData) => ipcRenderer.invoke('db-add-equipment', equipmentData),
  updateEquipment: (id, equipmentData) => ipcRenderer.invoke('db-update-equipment', id, equipmentData),
  deleteEquipment: (id) => ipcRenderer.invoke('db-delete-equipment', id),

  // العمليات المالية
  getFinancialRecords: () => ipcRenderer.invoke('db-get-financial-records'),
  addFinancialRecord: (recordData) => ipcRenderer.invoke('db-add-financial-record', recordData),
  getFinancialSummary: (startDate, endDate) => ipcRenderer.invoke('db-get-financial-summary', startDate, endDate),

  // عمليات المتجر
  getProducts: () => ipcRenderer.invoke('db-get-products'),
  addProduct: (productData) => ipcRenderer.invoke('db-add-product', productData),
  updateProduct: (id, productData) => ipcRenderer.invoke('db-update-product', id, productData),
  deleteProduct: (id) => ipcRenderer.invoke('db-delete-product', id),
  recordSale: (saleData) => ipcRenderer.invoke('db-record-sale', saleData),

  // عمليات النظام
  backupDatabase: () => ipcRenderer.invoke('system-backup-database'),
  importDatabase: (filePath) => ipcRenderer.invoke('system-import-database', filePath),
  getSettings: () => ipcRenderer.invoke('system-get-settings'),
  updateSettings: (settings) => ipcRenderer.invoke('system-update-settings', settings),

  // مستمعي الأحداث
  onBackupDatabase: (callback) => {
    ipcRenderer.on('backup-database', callback);
  },
  onImportDatabase: (callback) => {
    ipcRenderer.on('import-database', callback);
  },
};

// تعريض API للنافذة
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// تعريف النوع للنافذة
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
