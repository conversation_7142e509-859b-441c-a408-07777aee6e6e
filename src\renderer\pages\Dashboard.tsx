import React, { useState, useEffect } from 'react';
import {
  Users,
  CreditCard,
  Calendar,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface DashboardStats {
  totalMembers: number;
  activeSubscriptions: number;
  todayAttendance: number;
  monthlyRevenue: number;
  expiringSubscriptions: number;
  unpaidInvoices: number;
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalMembers: 0,
    activeSubscriptions: 0,
    todayAttendance: 0,
    monthlyRevenue: 0,
    expiringSubscriptions: 0,
    unpaidInvoices: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // محاكاة تحميل البيانات - سيتم استبدالها بالاستدعاءات الفعلية
      setTimeout(() => {
        setStats({
          totalMembers: 150,
          activeSubscriptions: 120,
          todayAttendance: 45,
          monthlyRevenue: 25000,
          expiringSubscriptions: 8,
          unpaidInvoices: 3
        });
        setLoading(false);
      }, 1000);
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setLoading(false);
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
    trend?: string;
  }> = ({ title, value, icon, color, trend }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-2xl font-bold ${color}`}>{value}</p>
          {trend && (
            <p className="text-sm text-green-600 flex items-center mt-1">
              <TrendingUp size={16} className="ml-1" />
              {trend}
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color.replace('text-', 'bg-').replace('-600', '-100')}`}>
          {icon}
        </div>
      </div>
    </div>
  );

  const AlertCard: React.FC<{
    title: string;
    count: number;
    type: 'warning' | 'danger' | 'info';
    icon: React.ReactNode;
  }> = ({ title, count, type, icon }) => {
    const colors = {
      warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      danger: 'bg-red-50 border-red-200 text-red-800',
      info: 'bg-blue-50 border-blue-200 text-blue-800'
    };

    return (
      <div className={`rounded-lg border p-4 ${colors[type]}`}>
        <div className="flex items-center">
          <div className="ml-3">
            {icon}
          </div>
          <div>
            <h3 className="text-sm font-medium">{title}</h3>
            <p className="text-lg font-bold">{count}</p>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner"></div>
        <span className="mr-3 text-gray-600">جاري تحميل البيانات...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">لوحة التحكم</h1>
        <button
          onClick={loadDashboardData}
          className="btn btn-primary"
        >
          تحديث البيانات
        </button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="إجمالي الأعضاء"
          value={stats.totalMembers}
          icon={<Users size={24} className="text-blue-600" />}
          color="text-blue-600"
          trend="+12% من الشهر الماضي"
        />
        
        <StatCard
          title="الاشتراكات الفعالة"
          value={stats.activeSubscriptions}
          icon={<CreditCard size={24} className="text-green-600" />}
          color="text-green-600"
          trend="+8% من الشهر الماضي"
        />
        
        <StatCard
          title="حضور اليوم"
          value={stats.todayAttendance}
          icon={<Calendar size={24} className="text-purple-600" />}
          color="text-purple-600"
        />
        
        <StatCard
          title="إيرادات الشهر"
          value={`${stats.monthlyRevenue.toLocaleString()} ريال`}
          icon={<DollarSign size={24} className="text-yellow-600" />}
          color="text-yellow-600"
          trend="+15% من الشهر الماضي"
        />
      </div>

      {/* Alerts Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <AlertCard
          title="اشتراكات قاربت على الانتهاء"
          count={stats.expiringSubscriptions}
          type="warning"
          icon={<Clock size={20} />}
        />
        
        <AlertCard
          title="فواتير غير مدفوعة"
          count={stats.unpaidInvoices}
          type="danger"
          icon={<AlertTriangle size={20} />}
        />
        
        <AlertCard
          title="أعضاء جدد هذا الأسبوع"
          count={12}
          type="info"
          icon={<CheckCircle size={20} />}
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="btn btn-primary flex items-center justify-center">
            <Users size={20} className="ml-2" />
            إضافة عضو جديد
          </button>
          
          <button className="btn btn-secondary flex items-center justify-center">
            <Calendar size={20} className="ml-2" />
            تسجيل حضور
          </button>
          
          <button className="btn btn-success flex items-center justify-center">
            <CreditCard size={20} className="ml-2" />
            تجديد اشتراك
          </button>
          
          <button className="btn btn-warning flex items-center justify-center">
            <DollarSign size={20} className="ml-2" />
            إضافة معاملة مالية
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">النشاط الأخير</h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full ml-3"></div>
              <span className="text-sm text-gray-700">تم تسجيل عضو جديد: أحمد محمد</span>
            </div>
            <span className="text-xs text-gray-500">منذ 5 دقائق</span>
          </div>
          
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full ml-3"></div>
              <span className="text-sm text-gray-700">تم تجديد اشتراك: سارة أحمد</span>
            </div>
            <span className="text-xs text-gray-500">منذ 15 دقيقة</span>
          </div>
          
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-yellow-500 rounded-full ml-3"></div>
              <span className="text-sm text-gray-700">تم إضافة معاملة مالية: 500 ريال</span>
            </div>
            <span className="text-xs text-gray-500">منذ 30 دقيقة</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
