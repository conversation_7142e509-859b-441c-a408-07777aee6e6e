import Database from 'better-sqlite3';
import * as path from 'path';
import { app } from 'electron';

export interface Member {
  id?: number;
  name: string;
  phone: string;
  email?: string;
  birthDate?: string;
  height?: number;
  weight?: number;
  healthNotes?: string;
  photo?: string;
  joinDate: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface Subscription {
  id?: number;
  memberId: number;
  packageType: string; // شهر، 3 شهور، 6 شهور، سنة
  price: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  createdAt?: string;
}

export interface Attendance {
  id?: number;
  memberId: number;
  checkInTime: string;
  checkOutTime?: string;
  date: string;
  createdAt?: string;
}

export interface Trainer {
  id?: number;
  name: string;
  phone: string;
  email?: string;
  specialization: string;
  salary: number;
  salaryType: 'fixed' | 'percentage';
  isActive: boolean;
  createdAt?: string;
}

export interface Equipment {
  id?: number;
  name: string;
  category: string;
  purchaseDate: string;
  price: number;
  condition: 'excellent' | 'good' | 'fair' | 'poor';
  maintenanceDate?: string;
  nextMaintenanceDate?: string;
  notes?: string;
  createdAt?: string;
}

export interface FinancialRecord {
  id?: number;
  type: 'income' | 'expense';
  category: string;
  amount: number;
  description: string;
  date: string;
  createdAt?: string;
}

export interface Product {
  id?: number;
  name: string;
  category: string;
  price: number;
  cost: number;
  stock: number;
  minStock: number;
  isActive: boolean;
  createdAt?: string;
}

export interface Sale {
  id?: number;
  productId: number;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  date: string;
  createdAt?: string;
}

export class DatabaseManager {
  private db: Database.Database | null = null;
  private dbPath: string;

  constructor() {
    const userDataPath = app.getPath('userData');
    this.dbPath = path.join(userDataPath, 'gym_database.db');
  }

  async initialize(): Promise<void> {
    try {
      this.db = new Database(this.dbPath);
      this.db.pragma('journal_mode = WAL');
      await this.createTables();
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // جدول الأعضاء
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS members (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT NOT NULL UNIQUE,
        email TEXT,
        birthDate TEXT,
        height REAL,
        weight REAL,
        healthNotes TEXT,
        photo TEXT,
        joinDate TEXT NOT NULL,
        isActive BOOLEAN DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول الاشتراكات
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS subscriptions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        memberId INTEGER NOT NULL,
        packageType TEXT NOT NULL,
        price REAL NOT NULL,
        startDate TEXT NOT NULL,
        endDate TEXT NOT NULL,
        isActive BOOLEAN DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (memberId) REFERENCES members (id) ON DELETE CASCADE
      )
    `);

    // جدول الحضور
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS attendance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        memberId INTEGER NOT NULL,
        checkInTime TEXT NOT NULL,
        checkOutTime TEXT,
        date TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (memberId) REFERENCES members (id) ON DELETE CASCADE
      )
    `);

    // جدول المدربين
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS trainers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT NOT NULL UNIQUE,
        email TEXT,
        specialization TEXT NOT NULL,
        salary REAL NOT NULL,
        salaryType TEXT NOT NULL CHECK (salaryType IN ('fixed', 'percentage')),
        isActive BOOLEAN DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول الأجهزة
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS equipment (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        purchaseDate TEXT NOT NULL,
        price REAL NOT NULL,
        condition TEXT NOT NULL CHECK (condition IN ('excellent', 'good', 'fair', 'poor')),
        maintenanceDate TEXT,
        nextMaintenanceDate TEXT,
        notes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول السجلات المالية
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS financial_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
        category TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT NOT NULL,
        date TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول المنتجات
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        price REAL NOT NULL,
        cost REAL NOT NULL,
        stock INTEGER NOT NULL DEFAULT 0,
        minStock INTEGER NOT NULL DEFAULT 0,
        isActive BOOLEAN DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول المبيعات
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS sales (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        productId INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        unitPrice REAL NOT NULL,
        totalPrice REAL NOT NULL,
        date TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (productId) REFERENCES products (id) ON DELETE CASCADE
      )
    `);

    // جدول الإعدادات
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // إدراج الإعدادات الافتراضية
    const defaultSettings = [
      { key: 'gym_name', value: 'نادي اللياقة البدنية' },
      { key: 'gym_address', value: '' },
      { key: 'gym_phone', value: '' },
      { key: 'gym_email', value: '' },
      { key: 'currency', value: 'ريال' },
      { key: 'language', value: 'ar' }
    ];

    const insertSetting = this.db.prepare(`
      INSERT OR IGNORE INTO settings (key, value) VALUES (?, ?)
    `);

    for (const setting of defaultSettings) {
      insertSetting.run(setting.key, setting.value);
    }
  }

  // ===== وظائف الأعضاء =====
  async getMembers(): Promise<Member[]> {
    if (!this.db) throw new Error('Database not initialized');
    const stmt = this.db.prepare('SELECT * FROM members ORDER BY name');
    return stmt.all() as Member[];
  }

  async getMemberById(id: number): Promise<Member | null> {
    if (!this.db) throw new Error('Database not initialized');
    const stmt = this.db.prepare('SELECT * FROM members WHERE id = ?');
    return stmt.get(id) as Member || null;
  }

  async addMember(member: Omit<Member, 'id' | 'createdAt' | 'updatedAt'>): Promise<Member> {
    if (!this.db) throw new Error('Database not initialized');
    const stmt = this.db.prepare(`
      INSERT INTO members (name, phone, email, birthDate, height, weight, healthNotes, photo, joinDate, isActive)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      member.name,
      member.phone,
      member.email || null,
      member.birthDate || null,
      member.height || null,
      member.weight || null,
      member.healthNotes || null,
      member.photo || null,
      member.joinDate,
      member.isActive ? 1 : 0
    );

    return this.getMemberById(result.lastInsertRowid as number) as Promise<Member>;
  }

  async updateMember(id: number, member: Partial<Member>): Promise<Member> {
    if (!this.db) throw new Error('Database not initialized');
    const stmt = this.db.prepare(`
      UPDATE members
      SET name = COALESCE(?, name),
          phone = COALESCE(?, phone),
          email = COALESCE(?, email),
          birthDate = COALESCE(?, birthDate),
          height = COALESCE(?, height),
          weight = COALESCE(?, weight),
          healthNotes = COALESCE(?, healthNotes),
          photo = COALESCE(?, photo),
          isActive = COALESCE(?, isActive),
          updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    stmt.run(
      member.name || null,
      member.phone || null,
      member.email || null,
      member.birthDate || null,
      member.height || null,
      member.weight || null,
      member.healthNotes || null,
      member.photo || null,
      member.isActive !== undefined ? (member.isActive ? 1 : 0) : null,
      id
    );

    return this.getMemberById(id) as Promise<Member>;
  }

  async deleteMember(id: number): Promise<boolean> {
    if (!this.db) throw new Error('Database not initialized');
    const stmt = this.db.prepare('DELETE FROM members WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // ===== وظائف الاشتراكات =====
  async getSubscriptions(): Promise<Subscription[]> {
    if (!this.db) throw new Error('Database not initialized');
    const stmt = this.db.prepare(`
      SELECT s.*, m.name as memberName
      FROM subscriptions s
      JOIN members m ON s.memberId = m.id
      ORDER BY s.createdAt DESC
    `);
    return stmt.all() as Subscription[];
  }

  async addSubscription(subscription: Omit<Subscription, 'id' | 'createdAt'>): Promise<Subscription> {
    if (!this.db) throw new Error('Database not initialized');
    const stmt = this.db.prepare(`
      INSERT INTO subscriptions (memberId, packageType, price, startDate, endDate, isActive)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      subscription.memberId,
      subscription.packageType,
      subscription.price,
      subscription.startDate,
      subscription.endDate,
      subscription.isActive ? 1 : 0
    );

    const getStmt = this.db.prepare('SELECT * FROM subscriptions WHERE id = ?');
    return getStmt.get(result.lastInsertRowid) as Subscription;
  }

  // ===== وظائف الحضور =====
  async recordAttendance(memberId: number): Promise<Attendance> {
    if (!this.db) throw new Error('Database not initialized');
    const now = new Date();
    const date = now.toISOString().split('T')[0];
    const time = now.toISOString();

    // التحقق من وجود سجل حضور لنفس اليوم
    const existingStmt = this.db.prepare(`
      SELECT * FROM attendance
      WHERE memberId = ? AND date = ? AND checkOutTime IS NULL
    `);
    const existing = existingStmt.get(memberId, date) as Attendance;

    if (existing) {
      // تسجيل خروج
      const updateStmt = this.db.prepare(`
        UPDATE attendance SET checkOutTime = ? WHERE id = ?
      `);
      updateStmt.run(time, existing.id);

      const getStmt = this.db.prepare('SELECT * FROM attendance WHERE id = ?');
      return getStmt.get(existing.id) as Attendance;
    } else {
      // تسجيل دخول جديد
      const insertStmt = this.db.prepare(`
        INSERT INTO attendance (memberId, checkInTime, date)
        VALUES (?, ?, ?)
      `);
      const result = insertStmt.run(memberId, time, date);

      const getStmt = this.db.prepare('SELECT * FROM attendance WHERE id = ?');
      return getStmt.get(result.lastInsertRowid) as Attendance;
    }
  }

  async getAttendance(startDate?: string, endDate?: string): Promise<Attendance[]> {
    if (!this.db) throw new Error('Database not initialized');

    let query = `
      SELECT a.*, m.name as memberName
      FROM attendance a
      JOIN members m ON a.memberId = m.id
    `;
    const params: any[] = [];

    if (startDate && endDate) {
      query += ' WHERE a.date BETWEEN ? AND ?';
      params.push(startDate, endDate);
    } else if (startDate) {
      query += ' WHERE a.date >= ?';
      params.push(startDate);
    } else if (endDate) {
      query += ' WHERE a.date <= ?';
      params.push(endDate);
    }

    query += ' ORDER BY a.date DESC, a.checkInTime DESC';

    const stmt = this.db.prepare(query);
    return stmt.all(...params) as Attendance[];
  }
