{"version": 3, "file": "simple-main.js", "sourceRoot": "", "sources": ["../../src/main/simple-main.js"], "names": [], "mappings": ";AAAA,MAAM,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;AACzD,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAE7B,IAAI,UAAU,CAAC;AAEf,SAAS,YAAY;IACnB,yBAAyB;IACzB,UAAU,GAAG,IAAI,aAAa,CAAC;QAC7B,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,GAAG;QACX,QAAQ,EAAE,GAAG;QACb,SAAS,EAAE,GAAG;QACd,IAAI,EAAE,IAAI,EAAE,uBAAuB;QACnC,eAAe,EAAE,SAAS,EAAE,mCAAmC;QAC/D,cAAc,EAAE;YACd,eAAe,EAAE,IAAI;YACrB,gBAAgB,EAAE,KAAK;YACvB,kBAAkB,EAAE,IAAI;SACzB;QACD,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,uBAAuB,CAAC;QACnD,aAAa,EAAE,SAAS;QACxB,eAAe,EAAE,KAAK;KACvB,CAAC,CAAC;IAEH,iBAAiB;IACjB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,+BAA+B,CAAC,CAAC,CAAC;IAE3E,kCAAkC;IAClC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QACnC,UAAU,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;IACxC,CAAC;IAED,wBAAwB;IACxB,MAAM,QAAQ,GAAG;QACf;YACE,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,eAAe;oBACtB,WAAW,EAAE,aAAa;oBAC1B,KAAK,EAAE,GAAG,EAAE;wBACV,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;oBAClC,CAAC;iBACF;gBACD;oBACE,KAAK,EAAE,SAAS;oBAChB,WAAW,EAAE,aAAa;oBAC1B,KAAK,EAAE,GAAG,EAAE;wBACV,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;oBAClC,CAAC;iBACF;gBACD,EAAE,IAAI,EAAE,WAAW,EAAE;gBACrB;oBACE,KAAK,EAAE,MAAM;oBACb,WAAW,EAAE,OAAO,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ;oBAC/D,KAAK,EAAE,GAAG,EAAE;wBACV,GAAG,CAAC,IAAI,EAAE,CAAC;oBACb,CAAC;iBACF;aACF;SACF;QACD;YACE,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE;gBACP,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE;gBACxC,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,kBAAkB,EAAE;gBAClD,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,cAAc,EAAE;gBACjD,EAAE,IAAI,EAAE,WAAW,EAAE;gBACrB,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,qBAAqB,EAAE;gBACnD,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;gBAClC,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;gBACnC,EAAE,IAAI,EAAE,WAAW,EAAE;gBACrB,EAAE,IAAI,EAAE,kBAAkB,EAAE,KAAK,EAAE,YAAY,EAAE;aAClD;SACF;QACD;YACE,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,aAAa;oBACpB,KAAK,EAAE,GAAG,EAAE;wBACV,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;wBACvC,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE;4BAChC,IAAI,EAAE,MAAM;4BACZ,KAAK,EAAE,aAAa;4BACpB,OAAO,EAAE,kBAAkB;4BAC3B,MAAM,EAAE,0FAA0F;yBACnG,CAAC,CAAC;oBACL,CAAC;iBACF;aACF;SACF;KACF,CAAC;IAEF,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC9C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAE9B,kCAAkC;IAClC,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QAC3B,UAAU,GAAG,IAAI,CAAC;IACpB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,iBAAiB;AACjB,GAAG,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;IACxB,YAAY,EAAE,CAAC;IAEf,GAAG,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE;QACtB,IAAI,aAAa,CAAC,aAAa,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/C,YAAY,EAAE,CAAC;QACjB,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,uCAAuC;AACvC,GAAG,CAAC,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;IAC/B,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAClC,GAAG,CAAC,IAAI,EAAE,CAAC;IACb,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,yBAAyB;AACzB,GAAG,CAAC,EAAE,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC7B,IAAI,UAAU,EAAE,CAAC;QACf,IAAI,UAAU,CAAC,WAAW,EAAE;YAAE,UAAU,CAAC,OAAO,EAAE,CAAC;QACnD,UAAU,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,oCAAoC;AACpC,MAAM,UAAU,GAAG,GAAG,CAAC,yBAAyB,EAAE,CAAC;AAEnD,IAAI,CAAC,UAAU,EAAE,CAAC;IAChB,GAAG,CAAC,IAAI,EAAE,CAAC;AACb,CAAC"}