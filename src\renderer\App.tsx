import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import Members from './pages/Members';
import Subscriptions from './pages/Subscriptions';
import Attendance from './pages/Attendance';
import Trainers from './pages/Trainers';
import Equipment from './pages/Equipment';
import Finance from './pages/Finance';
import Shop from './pages/Shop';
import Settings from './pages/Settings';

const App: React.FC = () => {
  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/members" element={<Members />} />
        <Route path="/subscriptions" element={<Subscriptions />} />
        <Route path="/attendance" element={<Attendance />} />
        <Route path="/trainers" element={<Trainers />} />
        <Route path="/equipment" element={<Equipment />} />
        <Route path="/finance" element={<Finance />} />
        <Route path="/shop" element={<Shop />} />
        <Route path="/settings" element={<Settings />} />
      </Routes>
    </Layout>
  );
};

export default App;
