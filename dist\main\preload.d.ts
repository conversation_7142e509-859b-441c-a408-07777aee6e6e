export interface ElectronAPI {
    getMembers: () => Promise<any[]>;
    addMember: (memberData: any) => Promise<any>;
    updateMember: (id: number, memberData: any) => Promise<any>;
    deleteMember: (id: number) => Promise<boolean>;
    getSubscriptions: () => Promise<any[]>;
    addSubscription: (subscriptionData: any) => Promise<any>;
    updateSubscription: (id: number, subscriptionData: any) => Promise<any>;
    deleteSubscription: (id: number) => Promise<boolean>;
    recordAttendance: (memberId: number) => Promise<any>;
    getAttendance: (startDate?: string, endDate?: string) => Promise<any[]>;
    getTrainers: () => Promise<any[]>;
    addTrainer: (trainerData: any) => Promise<any>;
    updateTrainer: (id: number, trainerData: any) => Promise<any>;
    deleteTrainer: (id: number) => Promise<boolean>;
    getEquipment: () => Promise<any[]>;
    addEquipment: (equipmentData: any) => Promise<any>;
    updateEquipment: (id: number, equipmentData: any) => Promise<any>;
    deleteEquipment: (id: number) => Promise<boolean>;
    getFinancialRecords: () => Promise<any[]>;
    addFinancialRecord: (recordData: any) => Promise<any>;
    getFinancialSummary: (startDate?: string, endDate?: string) => Promise<any>;
    getProducts: () => Promise<any[]>;
    addProduct: (productData: any) => Promise<any>;
    updateProduct: (id: number, productData: any) => Promise<any>;
    deleteProduct: (id: number) => Promise<boolean>;
    recordSale: (saleData: any) => Promise<any>;
    backupDatabase: () => Promise<boolean>;
    importDatabase: (filePath: string) => Promise<boolean>;
    getSettings: () => Promise<any>;
    updateSettings: (settings: any) => Promise<boolean>;
    onBackupDatabase: (callback: () => void) => void;
    onImportDatabase: (callback: () => void) => void;
}
declare global {
    interface Window {
        electronAPI: ElectronAPI;
    }
}
