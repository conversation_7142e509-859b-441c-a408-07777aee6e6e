"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// تعريف API للنافذة
const electronAPI = {
    // عمليات الأعضاء
    getMembers: () => electron_1.ipcRenderer.invoke('db-get-members'),
    addMember: (memberData) => electron_1.ipcRenderer.invoke('db-add-member', memberData),
    updateMember: (id, memberData) => electron_1.ipcRenderer.invoke('db-update-member', id, memberData),
    deleteMember: (id) => electron_1.ipcRenderer.invoke('db-delete-member', id),
    // عمليات الاشتراكات
    getSubscriptions: () => electron_1.ipcRenderer.invoke('db-get-subscriptions'),
    addSubscription: (subscriptionData) => electron_1.ipcRenderer.invoke('db-add-subscription', subscriptionData),
    updateSubscription: (id, subscriptionData) => electron_1.ipcRenderer.invoke('db-update-subscription', id, subscriptionData),
    deleteSubscription: (id) => electron_1.ipcRenderer.invoke('db-delete-subscription', id),
    // عمليات الحضور
    recordAttendance: (memberId) => electron_1.ipcRenderer.invoke('db-record-attendance', memberId),
    getAttendance: (startDate, endDate) => electron_1.ipcRenderer.invoke('db-get-attendance', startDate, endDate),
    // عمليات المدربين
    getTrainers: () => electron_1.ipcRenderer.invoke('db-get-trainers'),
    addTrainer: (trainerData) => electron_1.ipcRenderer.invoke('db-add-trainer', trainerData),
    updateTrainer: (id, trainerData) => electron_1.ipcRenderer.invoke('db-update-trainer', id, trainerData),
    deleteTrainer: (id) => electron_1.ipcRenderer.invoke('db-delete-trainer', id),
    // عمليات الأجهزة
    getEquipment: () => electron_1.ipcRenderer.invoke('db-get-equipment'),
    addEquipment: (equipmentData) => electron_1.ipcRenderer.invoke('db-add-equipment', equipmentData),
    updateEquipment: (id, equipmentData) => electron_1.ipcRenderer.invoke('db-update-equipment', id, equipmentData),
    deleteEquipment: (id) => electron_1.ipcRenderer.invoke('db-delete-equipment', id),
    // العمليات المالية
    getFinancialRecords: () => electron_1.ipcRenderer.invoke('db-get-financial-records'),
    addFinancialRecord: (recordData) => electron_1.ipcRenderer.invoke('db-add-financial-record', recordData),
    getFinancialSummary: (startDate, endDate) => electron_1.ipcRenderer.invoke('db-get-financial-summary', startDate, endDate),
    // عمليات المتجر
    getProducts: () => electron_1.ipcRenderer.invoke('db-get-products'),
    addProduct: (productData) => electron_1.ipcRenderer.invoke('db-add-product', productData),
    updateProduct: (id, productData) => electron_1.ipcRenderer.invoke('db-update-product', id, productData),
    deleteProduct: (id) => electron_1.ipcRenderer.invoke('db-delete-product', id),
    recordSale: (saleData) => electron_1.ipcRenderer.invoke('db-record-sale', saleData),
    // عمليات النظام
    backupDatabase: () => electron_1.ipcRenderer.invoke('system-backup-database'),
    importDatabase: (filePath) => electron_1.ipcRenderer.invoke('system-import-database', filePath),
    getSettings: () => electron_1.ipcRenderer.invoke('system-get-settings'),
    updateSettings: (settings) => electron_1.ipcRenderer.invoke('system-update-settings', settings),
    // مستمعي الأحداث
    onBackupDatabase: (callback) => {
        electron_1.ipcRenderer.on('backup-database', callback);
    },
    onImportDatabase: (callback) => {
        electron_1.ipcRenderer.on('import-database', callback);
    },
};
// تعريض API للنافذة
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
//# sourceMappingURL=preload.js.map