/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m17 15-5.5 5.5L9 18", key: "15q87x" }],
  ["path", { d: "M5 17.743A7 7 0 1 1 15.71 10h1.79a4.5 4.5 0 0 1 1.5 8.742", key: "9ho6ki" }]
];
const CloudCheck = createLucideIcon("cloud-check", __iconNode);

export { __iconNode, CloudCheck as default };
//# sourceMappingURL=cloud-check.js.map
