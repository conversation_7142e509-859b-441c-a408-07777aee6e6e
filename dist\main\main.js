"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const DatabaseManager_1 = require("../database/DatabaseManager");
class GymApp {
    constructor() {
        this.mainWindow = null;
        this.dbManager = new DatabaseManager_1.DatabaseManager();
    }
    createWindow() {
        // إنشاء النافذة الرئيسية
        this.mainWindow = new electron_1.BrowserWindow({
            height: 800,
            width: 1200,
            minHeight: 600,
            minWidth: 800,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: path.join(__dirname, 'preload.js'),
            },
            icon: path.join(__dirname, '../../assets/icon.png'),
            titleBarStyle: 'default',
            show: true, // إظهار النافذة مباشرة
            backgroundColor: '#ffffff', // خلفية بيضاء لتجنب الشاشة السوداء
        });
        // تحميل التطبيق
        const isDev = process.argv.includes('--dev');
        if (isDev) {
            this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
            this.mainWindow.webContents.openDevTools();
        }
        else {
            this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
        }
        // النافذة ستظهر مباشرة بدون انتظار
        // إغلاق التطبيق عند إغلاق النافذة الرئيسية
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
    }
    setupMenu() {
        const template = [
            {
                label: 'ملف',
                submenu: [
                    {
                        label: 'نسخة احتياطية',
                        accelerator: 'CmdOrCtrl+B',
                        click: () => {
                            this.mainWindow?.webContents.send('backup-database');
                        }
                    },
                    {
                        label: 'استيراد',
                        accelerator: 'CmdOrCtrl+I',
                        click: () => {
                            this.mainWindow?.webContents.send('import-database');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'خروج',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            electron_1.app.quit();
                        }
                    }
                ]
            },
            {
                label: 'عرض',
                submenu: [
                    { role: 'reload', label: 'إعادة تحميل' },
                    { role: 'forceReload', label: 'إعادة تحميل قسري' },
                    { role: 'toggleDevTools', label: 'أدوات المطور' },
                    { type: 'separator' },
                    { role: 'resetZoom', label: 'إعادة تعيين التكبير' },
                    { role: 'zoomIn', label: 'تكبير' },
                    { role: 'zoomOut', label: 'تصغير' },
                    { type: 'separator' },
                    { role: 'togglefullscreen', label: 'ملء الشاشة' }
                ]
            }
        ];
        const menu = electron_1.Menu.buildFromTemplate(template);
        electron_1.Menu.setApplicationMenu(menu);
    }
    setupIpcHandlers() {
        // معالجات قاعدة البيانات
        electron_1.ipcMain.handle('db-get-members', async () => {
            return await this.dbManager.getMembers();
        });
        electron_1.ipcMain.handle('db-add-member', async (_, memberData) => {
            return await this.dbManager.addMember(memberData);
        });
        electron_1.ipcMain.handle('db-update-member', async (_, id, memberData) => {
            return await this.dbManager.updateMember(id, memberData);
        });
        electron_1.ipcMain.handle('db-delete-member', async (_, id) => {
            return await this.dbManager.deleteMember(id);
        });
        // معالجات الاشتراكات
        electron_1.ipcMain.handle('db-get-subscriptions', async () => {
            return await this.dbManager.getSubscriptions();
        });
        electron_1.ipcMain.handle('db-add-subscription', async (_, subscriptionData) => {
            return await this.dbManager.addSubscription(subscriptionData);
        });
        // معالجات الحضور
        electron_1.ipcMain.handle('db-record-attendance', async (_, memberId) => {
            return await this.dbManager.recordAttendance(memberId);
        });
        electron_1.ipcMain.handle('db-get-attendance', async (_, startDate, endDate) => {
            return await this.dbManager.getAttendance(startDate, endDate);
        });
    }
    async initialize() {
        // انتظار جاهزية Electron
        await electron_1.app.whenReady();
        // إنشاء قاعدة البيانات
        await this.dbManager.initialize();
        // إعداد التطبيق
        this.createWindow();
        this.setupMenu();
        this.setupIpcHandlers();
        // معالجة إعادة تفعيل التطبيق على macOS
        electron_1.app.on('activate', () => {
            if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                this.createWindow();
            }
        });
        // إغلاق التطبيق عند إغلاق جميع النوافذ
        electron_1.app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                electron_1.app.quit();
            }
        });
    }
}
// تشغيل التطبيق
const gymApp = new GymApp();
gymApp.initialize().catch(console.error);
//# sourceMappingURL=main.js.map