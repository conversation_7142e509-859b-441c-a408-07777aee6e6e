"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const lucide_react_1 = require("lucide-react");
const Dashboard = () => {
    const [stats, setStats] = (0, react_1.useState)({
        totalMembers: 0,
        activeSubscriptions: 0,
        todayAttendance: 0,
        monthlyRevenue: 0,
        expiringSubscriptions: 0,
        unpaidInvoices: 0
    });
    const [loading, setLoading] = (0, react_1.useState)(true);
    (0, react_1.useEffect)(() => {
        loadDashboardData();
    }, []);
    const loadDashboardData = async () => {
        try {
            setLoading(true);
            // محاكاة تحميل البيانات - سيتم استبدالها بالاستدعاءات الفعلية
            setTimeout(() => {
                setStats({
                    totalMembers: 150,
                    activeSubscriptions: 120,
                    todayAttendance: 45,
                    monthlyRevenue: 25000,
                    expiringSubscriptions: 8,
                    unpaidInvoices: 3
                });
                setLoading(false);
            }, 1000);
        }
        catch (error) {
            console.error('Error loading dashboard data:', error);
            setLoading(false);
        }
    };
    const StatCard = ({ title, value, icon, color, trend }) => ((0, jsx_runtime_1.jsx)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6", children: (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-between", children: [(0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("p", { className: "text-sm font-medium text-gray-600", children: title }), (0, jsx_runtime_1.jsx)("p", { className: `text-2xl font-bold ${color}`, children: value }), trend && ((0, jsx_runtime_1.jsxs)("p", { className: "text-sm text-green-600 flex items-center mt-1", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.TrendingUp, { size: 16, className: "ml-1" }), trend] }))] }), (0, jsx_runtime_1.jsx)("div", { className: `p-3 rounded-full ${color.replace('text-', 'bg-').replace('-600', '-100')}`, children: icon })] }) }));
    const AlertCard = ({ title, count, type, icon }) => {
        const colors = {
            warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
            danger: 'bg-red-50 border-red-200 text-red-800',
            info: 'bg-blue-50 border-blue-200 text-blue-800'
        };
        return ((0, jsx_runtime_1.jsx)("div", { className: `rounded-lg border p-4 ${colors[type]}`, children: (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center", children: [(0, jsx_runtime_1.jsx)("div", { className: "ml-3", children: icon }), (0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("h3", { className: "text-sm font-medium", children: title }), (0, jsx_runtime_1.jsx)("p", { className: "text-lg font-bold", children: count })] })] }) }));
    };
    if (loading) {
        return ((0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-center h-64", children: [(0, jsx_runtime_1.jsx)("div", { className: "loading-spinner" }), (0, jsx_runtime_1.jsx)("span", { className: "mr-3 text-gray-600", children: "\u062C\u0627\u0631\u064A \u062A\u062D\u0645\u064A\u0644 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A..." })] }));
    }
    return ((0, jsx_runtime_1.jsxs)("div", { className: "space-y-6", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-between", children: [(0, jsx_runtime_1.jsx)("h1", { className: "text-2xl font-bold text-gray-900", children: "\u0644\u0648\u062D\u0629 \u0627\u0644\u062A\u062D\u0643\u0645" }), (0, jsx_runtime_1.jsx)("button", { onClick: loadDashboardData, className: "btn btn-primary", children: "\u062A\u062D\u062F\u064A\u062B \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6", children: [(0, jsx_runtime_1.jsx)(StatCard, { title: "\u0625\u062C\u0645\u0627\u0644\u064A \u0627\u0644\u0623\u0639\u0636\u0627\u0621", value: stats.totalMembers, icon: (0, jsx_runtime_1.jsx)(lucide_react_1.Users, { size: 24, className: "text-blue-600" }), color: "text-blue-600", trend: "+12% \u0645\u0646 \u0627\u0644\u0634\u0647\u0631 \u0627\u0644\u0645\u0627\u0636\u064A" }), (0, jsx_runtime_1.jsx)(StatCard, { title: "\u0627\u0644\u0627\u0634\u062A\u0631\u0627\u0643\u0627\u062A \u0627\u0644\u0641\u0639\u0627\u0644\u0629", value: stats.activeSubscriptions, icon: (0, jsx_runtime_1.jsx)(lucide_react_1.CreditCard, { size: 24, className: "text-green-600" }), color: "text-green-600", trend: "+8% \u0645\u0646 \u0627\u0644\u0634\u0647\u0631 \u0627\u0644\u0645\u0627\u0636\u064A" }), (0, jsx_runtime_1.jsx)(StatCard, { title: "\u062D\u0636\u0648\u0631 \u0627\u0644\u064A\u0648\u0645", value: stats.todayAttendance, icon: (0, jsx_runtime_1.jsx)(lucide_react_1.Calendar, { size: 24, className: "text-purple-600" }), color: "text-purple-600" }), (0, jsx_runtime_1.jsx)(StatCard, { title: "\u0625\u064A\u0631\u0627\u062F\u0627\u062A \u0627\u0644\u0634\u0647\u0631", value: `${stats.monthlyRevenue.toLocaleString()} ريال`, icon: (0, jsx_runtime_1.jsx)(lucide_react_1.DollarSign, { size: 24, className: "text-yellow-600" }), color: "text-yellow-600", trend: "+15% \u0645\u0646 \u0627\u0644\u0634\u0647\u0631 \u0627\u0644\u0645\u0627\u0636\u064A" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-6", children: [(0, jsx_runtime_1.jsx)(AlertCard, { title: "\u0627\u0634\u062A\u0631\u0627\u0643\u0627\u062A \u0642\u0627\u0631\u0628\u062A \u0639\u0644\u0649 \u0627\u0644\u0627\u0646\u062A\u0647\u0627\u0621", count: stats.expiringSubscriptions, type: "warning", icon: (0, jsx_runtime_1.jsx)(lucide_react_1.Clock, { size: 20 }) }), (0, jsx_runtime_1.jsx)(AlertCard, { title: "\u0641\u0648\u0627\u062A\u064A\u0631 \u063A\u064A\u0631 \u0645\u062F\u0641\u0648\u0639\u0629", count: stats.unpaidInvoices, type: "danger", icon: (0, jsx_runtime_1.jsx)(lucide_react_1.AlertTriangle, { size: 20 }) }), (0, jsx_runtime_1.jsx)(AlertCard, { title: "\u0623\u0639\u0636\u0627\u0621 \u062C\u062F\u062F \u0647\u0630\u0627 \u0627\u0644\u0623\u0633\u0628\u0648\u0639", count: 12, type: "info", icon: (0, jsx_runtime_1.jsx)(lucide_react_1.CheckCircle, { size: 20 }) })] }), (0, jsx_runtime_1.jsxs)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6", children: [(0, jsx_runtime_1.jsx)("h2", { className: "text-lg font-semibold text-gray-900 mb-4", children: "\u0625\u062C\u0631\u0627\u0621\u0627\u062A \u0633\u0631\u064A\u0639\u0629" }), (0, jsx_runtime_1.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4", children: [(0, jsx_runtime_1.jsxs)("button", { className: "btn btn-primary flex items-center justify-center", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Users, { size: 20, className: "ml-2" }), "\u0625\u0636\u0627\u0641\u0629 \u0639\u0636\u0648 \u062C\u062F\u064A\u062F"] }), (0, jsx_runtime_1.jsxs)("button", { className: "btn btn-secondary flex items-center justify-center", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Calendar, { size: 20, className: "ml-2" }), "\u062A\u0633\u062C\u064A\u0644 \u062D\u0636\u0648\u0631"] }), (0, jsx_runtime_1.jsxs)("button", { className: "btn btn-success flex items-center justify-center", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.CreditCard, { size: 20, className: "ml-2" }), "\u062A\u062C\u062F\u064A\u062F \u0627\u0634\u062A\u0631\u0627\u0643"] }), (0, jsx_runtime_1.jsxs)("button", { className: "btn btn-warning flex items-center justify-center", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.DollarSign, { size: 20, className: "ml-2" }), "\u0625\u0636\u0627\u0641\u0629 \u0645\u0639\u0627\u0645\u0644\u0629 \u0645\u0627\u0644\u064A\u0629"] })] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6", children: [(0, jsx_runtime_1.jsx)("h2", { className: "text-lg font-semibold text-gray-900 mb-4", children: "\u0627\u0644\u0646\u0634\u0627\u0637 \u0627\u0644\u0623\u062E\u064A\u0631" }), (0, jsx_runtime_1.jsxs)("div", { className: "space-y-3", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-between py-2 border-b border-gray-100", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-2 h-2 bg-green-500 rounded-full ml-3" }), (0, jsx_runtime_1.jsx)("span", { className: "text-sm text-gray-700", children: "\u062A\u0645 \u062A\u0633\u062C\u064A\u0644 \u0639\u0636\u0648 \u062C\u062F\u064A\u062F: \u0623\u062D\u0645\u062F \u0645\u062D\u0645\u062F" })] }), (0, jsx_runtime_1.jsx)("span", { className: "text-xs text-gray-500", children: "\u0645\u0646\u0630 5 \u062F\u0642\u0627\u0626\u0642" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-between py-2 border-b border-gray-100", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-2 h-2 bg-blue-500 rounded-full ml-3" }), (0, jsx_runtime_1.jsx)("span", { className: "text-sm text-gray-700", children: "\u062A\u0645 \u062A\u062C\u062F\u064A\u062F \u0627\u0634\u062A\u0631\u0627\u0643: \u0633\u0627\u0631\u0629 \u0623\u062D\u0645\u062F" })] }), (0, jsx_runtime_1.jsx)("span", { className: "text-xs text-gray-500", children: "\u0645\u0646\u0630 15 \u062F\u0642\u064A\u0642\u0629" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "flex items-center justify-between py-2 border-b border-gray-100", children: [(0, jsx_runtime_1.jsxs)("div", { className: "flex items-center", children: [(0, jsx_runtime_1.jsx)("div", { className: "w-2 h-2 bg-yellow-500 rounded-full ml-3" }), (0, jsx_runtime_1.jsx)("span", { className: "text-sm text-gray-700", children: "\u062A\u0645 \u0625\u0636\u0627\u0641\u0629 \u0645\u0639\u0627\u0645\u0644\u0629 \u0645\u0627\u0644\u064A\u0629: 500 \u0631\u064A\u0627\u0644" })] }), (0, jsx_runtime_1.jsx)("span", { className: "text-xs text-gray-500", children: "\u0645\u0646\u0630 30 \u062F\u0642\u064A\u0642\u0629" })] })] })] })] }));
};
exports.default = Dashboard;
//# sourceMappingURL=Dashboard.js.map