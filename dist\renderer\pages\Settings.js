"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const lucide_react_1 = require("lucide-react");
const Settings = () => {
    const [gymSettings, setGymSettings] = (0, react_1.useState)({
        gymName: 'نادي اللياقة البدنية',
        gymAddress: '',
        gymPhone: '',
        gymEmail: '',
        currency: 'ريال'
    });
    const handleSaveSettings = () => {
        // سيتم ربطها بقاعدة البيانات لاحقاً
        alert('تم حفظ الإعدادات بنجاح');
    };
    const openWhatsApp = () => {
        window.open('https://wa.me/966225396729', '_blank');
    };
    const openFacebook = () => {
        window.open('https://web.facebook.com/profile.php?id=61578888731370', '_blank');
    };
    return ((0, jsx_runtime_1.jsxs)("div", { className: "space-y-6", children: [(0, jsx_runtime_1.jsx)("div", { className: "flex items-center justify-between", children: (0, jsx_runtime_1.jsx)("h1", { className: "text-2xl font-bold text-gray-900", children: "\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A" }) }), (0, jsx_runtime_1.jsxs)("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: [(0, jsx_runtime_1.jsxs)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6", children: [(0, jsx_runtime_1.jsx)("div", { className: "card-header", children: (0, jsx_runtime_1.jsxs)("h2", { className: "card-title flex items-center", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Settings, { size: 20, className: "ml-2" }), "\u0625\u0639\u062F\u0627\u062F\u0627\u062A \u0627\u0644\u062C\u064A\u0645"] }) }), (0, jsx_runtime_1.jsxs)("div", { className: "space-y-4", children: [(0, jsx_runtime_1.jsxs)("div", { className: "form-group", children: [(0, jsx_runtime_1.jsx)("label", { className: "form-label", children: "\u0627\u0633\u0645 \u0627\u0644\u062C\u064A\u0645" }), (0, jsx_runtime_1.jsx)("input", { type: "text", value: gymSettings.gymName, onChange: (e) => setGymSettings({ ...gymSettings, gymName: e.target.value }), className: "form-input" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "form-group", children: [(0, jsx_runtime_1.jsx)("label", { className: "form-label", children: "\u0627\u0644\u0639\u0646\u0648\u0627\u0646" }), (0, jsx_runtime_1.jsx)("input", { type: "text", value: gymSettings.gymAddress, onChange: (e) => setGymSettings({ ...gymSettings, gymAddress: e.target.value }), className: "form-input" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "form-group", children: [(0, jsx_runtime_1.jsx)("label", { className: "form-label", children: "\u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062A\u0641" }), (0, jsx_runtime_1.jsx)("input", { type: "text", value: gymSettings.gymPhone, onChange: (e) => setGymSettings({ ...gymSettings, gymPhone: e.target.value }), className: "form-input" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "form-group", children: [(0, jsx_runtime_1.jsx)("label", { className: "form-label", children: "\u0627\u0644\u0628\u0631\u064A\u062F \u0627\u0644\u0625\u0644\u0643\u062A\u0631\u0648\u0646\u064A" }), (0, jsx_runtime_1.jsx)("input", { type: "email", value: gymSettings.gymEmail, onChange: (e) => setGymSettings({ ...gymSettings, gymEmail: e.target.value }), className: "form-input" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "form-group", children: [(0, jsx_runtime_1.jsx)("label", { className: "form-label", children: "\u0627\u0644\u0639\u0645\u0644\u0629" }), (0, jsx_runtime_1.jsxs)("select", { value: gymSettings.currency, onChange: (e) => setGymSettings({ ...gymSettings, currency: e.target.value }), className: "form-select", children: [(0, jsx_runtime_1.jsx)("option", { value: "\u0631\u064A\u0627\u0644", children: "\u0631\u064A\u0627\u0644 \u0633\u0639\u0648\u062F\u064A" }), (0, jsx_runtime_1.jsx)("option", { value: "\u062F\u0631\u0647\u0645", children: "\u062F\u0631\u0647\u0645 \u0625\u0645\u0627\u0631\u0627\u062A\u064A" }), (0, jsx_runtime_1.jsx)("option", { value: "\u062F\u064A\u0646\u0627\u0631", children: "\u062F\u064A\u0646\u0627\u0631 \u0643\u0648\u064A\u062A\u064A" }), (0, jsx_runtime_1.jsx)("option", { value: "\u062C\u0646\u064A\u0647", children: "\u062C\u0646\u064A\u0647 \u0645\u0635\u0631\u064A" })] })] }), (0, jsx_runtime_1.jsxs)("button", { onClick: handleSaveSettings, className: "btn btn-primary w-full flex items-center justify-center", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Save, { size: 20, className: "ml-2" }), "\u062D\u0641\u0638 \u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A"] })] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "space-y-6", children: [(0, jsx_runtime_1.jsxs)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6", children: [(0, jsx_runtime_1.jsx)("div", { className: "card-header", children: (0, jsx_runtime_1.jsx)("h2", { className: "card-title", children: "\u0627\u0644\u062A\u0648\u0627\u0635\u0644 \u0648\u0627\u0644\u062F\u0639\u0645" }) }), (0, jsx_runtime_1.jsxs)("div", { className: "space-y-4", children: [(0, jsx_runtime_1.jsxs)("button", { onClick: openWhatsApp, className: "w-full bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-4 rounded-lg flex items-center justify-center transition-colors duration-200", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.MessageCircle, { size: 20, className: "ml-2" }), "\u0627\u0644\u062F\u0639\u0645 \u0627\u0644\u0641\u0646\u064A"] }), (0, jsx_runtime_1.jsxs)("button", { onClick: openFacebook, className: "w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg flex items-center justify-center transition-colors duration-200", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Facebook, { size: 20, className: "ml-2" }), "\u0645\u062A\u0627\u0628\u0639\u0629 \u0635\u0641\u062D\u062A\u0646\u0627"] })] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6", children: [(0, jsx_runtime_1.jsx)("div", { className: "card-header", children: (0, jsx_runtime_1.jsxs)("h2", { className: "card-title flex items-center", children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Info, { size: 20, className: "ml-2" }), "\u062D\u0648\u0644 \u0627\u0644\u062A\u0637\u0628\u064A\u0642"] }) }), (0, jsx_runtime_1.jsxs)("div", { className: "space-y-4 text-center", children: [(0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)("h3", { className: "text-lg font-semibold text-gray-900", children: "\u0646\u0638\u0627\u0645 \u0625\u062F\u0627\u0631\u0629 \u0627\u0644\u062C\u064A\u0645" }), (0, jsx_runtime_1.jsx)("p", { className: "text-gray-600", children: "\u0627\u0644\u0625\u0635\u062F\u0627\u0631 1.0.0" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "border-t border-gray-200 pt-4", children: [(0, jsx_runtime_1.jsx)("p", { className: "text-sm text-gray-600 mb-2", children: "\u0627\u0644\u0645\u0637\u0648\u0631:" }), (0, jsx_runtime_1.jsx)("p", { className: "font-medium text-gray-900", children: "Eng/ Hossam Osama" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "border-t border-gray-200 pt-4", children: [(0, jsx_runtime_1.jsx)("p", { className: "text-sm text-gray-600 mb-2", children: "\u062A\u0637\u0648\u064A\u0631:" }), (0, jsx_runtime_1.jsx)("p", { className: "font-bold text-blue-600 text-lg", children: "H - TECH" })] }), (0, jsx_runtime_1.jsx)("div", { className: "border-t border-gray-200 pt-4", children: (0, jsx_runtime_1.jsx)("p", { className: "text-xs text-gray-500", children: "\u062C\u0645\u064A\u0639 \u0627\u0644\u062D\u0642\u0648\u0642 \u0645\u062D\u0641\u0648\u0638\u0629 \u00A9 2024" }) })] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "bg-white rounded-lg shadow-sm border border-gray-200 p-6", children: [(0, jsx_runtime_1.jsx)("div", { className: "card-header", children: (0, jsx_runtime_1.jsx)("h2", { className: "card-title", children: "\u0627\u0644\u0646\u0633\u062E \u0627\u0644\u0627\u062D\u062A\u064A\u0627\u0637\u064A" }) }), (0, jsx_runtime_1.jsxs)("div", { className: "space-y-4", children: [(0, jsx_runtime_1.jsx)("button", { className: "btn btn-secondary w-full", children: "\u0625\u0646\u0634\u0627\u0621 \u0646\u0633\u062E\u0629 \u0627\u062D\u062A\u064A\u0627\u0637\u064A\u0629" }), (0, jsx_runtime_1.jsx)("button", { className: "btn btn-outline w-full border-gray-300 text-gray-700", children: "\u0627\u0633\u062A\u064A\u0631\u0627\u062F \u0646\u0633\u062E\u0629 \u0627\u062D\u062A\u064A\u0627\u0637\u064A\u0629" })] })] })] })] })] }));
};
exports.default = Settings;
//# sourceMappingURL=Settings.js.map