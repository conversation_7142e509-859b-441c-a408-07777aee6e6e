"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseManager = void 0;
const better_sqlite3_1 = __importDefault(require("better-sqlite3"));
const path = __importStar(require("path"));
const electron_1 = require("electron");
class DatabaseManager {
    constructor() {
        this.db = null;
        const userDataPath = electron_1.app.getPath('userData');
        this.dbPath = path.join(userDataPath, 'gym_database.db');
    }
    async initialize() {
        try {
            this.db = new better_sqlite3_1.default(this.dbPath);
            this.db.pragma('journal_mode = WAL');
            await this.createTables();
            console.log('Database initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize database:', error);
            throw error;
        }
    }
    async createTables() {
        if (!this.db)
            throw new Error('Database not initialized');
        // جدول الأعضاء
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS members (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT NOT NULL UNIQUE,
        email TEXT,
        birthDate TEXT,
        height REAL,
        weight REAL,
        healthNotes TEXT,
        photo TEXT,
        joinDate TEXT NOT NULL,
        isActive BOOLEAN DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
        // جدول الاشتراكات
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS subscriptions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        memberId INTEGER NOT NULL,
        packageType TEXT NOT NULL,
        price REAL NOT NULL,
        startDate TEXT NOT NULL,
        endDate TEXT NOT NULL,
        isActive BOOLEAN DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (memberId) REFERENCES members (id) ON DELETE CASCADE
      )
    `);
        // جدول الحضور
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS attendance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        memberId INTEGER NOT NULL,
        checkInTime TEXT NOT NULL,
        checkOutTime TEXT,
        date TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (memberId) REFERENCES members (id) ON DELETE CASCADE
      )
    `);
        // جدول المدربين
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS trainers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT NOT NULL UNIQUE,
        email TEXT,
        specialization TEXT NOT NULL,
        salary REAL NOT NULL,
        salaryType TEXT NOT NULL CHECK (salaryType IN ('fixed', 'percentage')),
        isActive BOOLEAN DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
        // جدول الأجهزة
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS equipment (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        purchaseDate TEXT NOT NULL,
        price REAL NOT NULL,
        condition TEXT NOT NULL CHECK (condition IN ('excellent', 'good', 'fair', 'poor')),
        maintenanceDate TEXT,
        nextMaintenanceDate TEXT,
        notes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
        // جدول السجلات المالية
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS financial_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
        category TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT NOT NULL,
        date TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
        // جدول المنتجات
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        price REAL NOT NULL,
        cost REAL NOT NULL,
        stock INTEGER NOT NULL DEFAULT 0,
        minStock INTEGER NOT NULL DEFAULT 0,
        isActive BOOLEAN DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
        // جدول المبيعات
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS sales (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        productId INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        unitPrice REAL NOT NULL,
        totalPrice REAL NOT NULL,
        date TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (productId) REFERENCES products (id) ON DELETE CASCADE
      )
    `);
        // جدول الإعدادات
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
        // إدراج الإعدادات الافتراضية
        const defaultSettings = [
            { key: 'gym_name', value: 'نادي اللياقة البدنية' },
            { key: 'gym_address', value: '' },
            { key: 'gym_phone', value: '' },
            { key: 'gym_email', value: '' },
            { key: 'currency', value: 'ريال' },
            { key: 'language', value: 'ar' }
        ];
        const insertSetting = this.db.prepare(`
      INSERT OR IGNORE INTO settings (key, value) VALUES (?, ?)
    `);
        for (const setting of defaultSettings) {
            insertSetting.run(setting.key, setting.value);
        }
    }
    // ===== وظائف الأعضاء =====
    async getMembers() {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare('SELECT * FROM members ORDER BY name');
        return stmt.all();
    }
    async getMemberById(id) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare('SELECT * FROM members WHERE id = ?');
        return stmt.get(id) || null;
    }
    async addMember(member) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
      INSERT INTO members (name, phone, email, birthDate, height, weight, healthNotes, photo, joinDate, isActive)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
        const result = stmt.run(member.name, member.phone, member.email || null, member.birthDate || null, member.height || null, member.weight || null, member.healthNotes || null, member.photo || null, member.joinDate, member.isActive ? 1 : 0);
        return this.getMemberById(result.lastInsertRowid);
    }
    async updateMember(id, member) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
      UPDATE members
      SET name = COALESCE(?, name),
          phone = COALESCE(?, phone),
          email = COALESCE(?, email),
          birthDate = COALESCE(?, birthDate),
          height = COALESCE(?, height),
          weight = COALESCE(?, weight),
          healthNotes = COALESCE(?, healthNotes),
          photo = COALESCE(?, photo),
          isActive = COALESCE(?, isActive),
          updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
        stmt.run(member.name || null, member.phone || null, member.email || null, member.birthDate || null, member.height || null, member.weight || null, member.healthNotes || null, member.photo || null, member.isActive !== undefined ? (member.isActive ? 1 : 0) : null, id);
        return this.getMemberById(id);
    }
    async deleteMember(id) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare('DELETE FROM members WHERE id = ?');
        const result = stmt.run(id);
        return result.changes > 0;
    }
    // ===== وظائف الاشتراكات =====
    async getSubscriptions() {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
      SELECT s.*, m.name as memberName
      FROM subscriptions s
      JOIN members m ON s.memberId = m.id
      ORDER BY s.createdAt DESC
    `);
        return stmt.all();
    }
    async addSubscription(subscription) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
      INSERT INTO subscriptions (memberId, packageType, price, startDate, endDate, isActive)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
        const result = stmt.run(subscription.memberId, subscription.packageType, subscription.price, subscription.startDate, subscription.endDate, subscription.isActive ? 1 : 0);
        const getStmt = this.db.prepare('SELECT * FROM subscriptions WHERE id = ?');
        return getStmt.get(result.lastInsertRowid);
    }
    // ===== وظائف الحضور =====
    async recordAttendance(memberId) {
        if (!this.db)
            throw new Error('Database not initialized');
        const now = new Date();
        const date = now.toISOString().split('T')[0];
        const time = now.toISOString();
        // التحقق من وجود سجل حضور لنفس اليوم
        const existingStmt = this.db.prepare(`
      SELECT * FROM attendance
      WHERE memberId = ? AND date = ? AND checkOutTime IS NULL
    `);
        const existing = existingStmt.get(memberId, date);
        if (existing) {
            // تسجيل خروج
            const updateStmt = this.db.prepare(`
        UPDATE attendance SET checkOutTime = ? WHERE id = ?
      `);
            updateStmt.run(time, existing.id);
            const getStmt = this.db.prepare('SELECT * FROM attendance WHERE id = ?');
            return getStmt.get(existing.id);
        }
        else {
            // تسجيل دخول جديد
            const insertStmt = this.db.prepare(`
        INSERT INTO attendance (memberId, checkInTime, date)
        VALUES (?, ?, ?)
      `);
            const result = insertStmt.run(memberId, time, date);
            const getStmt = this.db.prepare('SELECT * FROM attendance WHERE id = ?');
            return getStmt.get(result.lastInsertRowid);
        }
    }
    async getAttendance(startDate, endDate) {
        if (!this.db)
            throw new Error('Database not initialized');
        let query = `
      SELECT a.*, m.name as memberName
      FROM attendance a
      JOIN members m ON a.memberId = m.id
    `;
        const params = [];
        if (startDate && endDate) {
            query += ' WHERE a.date BETWEEN ? AND ?';
            params.push(startDate, endDate);
        }
        else if (startDate) {
            query += ' WHERE a.date >= ?';
            params.push(startDate);
        }
        else if (endDate) {
            query += ' WHERE a.date <= ?';
            params.push(endDate);
        }
        query += ' ORDER BY a.date DESC, a.checkInTime DESC';
        const stmt = this.db.prepare(query);
        return stmt.all(...params);
    }
    // ===== وظائف المالية =====
    async getFinancialRecords() {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare('SELECT * FROM financial_records ORDER BY date DESC');
        return stmt.all();
    }
    async addFinancialRecord(record) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
      INSERT INTO financial_records (type, category, amount, description, date)
      VALUES (?, ?, ?, ?, ?)
    `);
        const result = stmt.run(record.type, record.category, record.amount, record.description, record.date);
        const getStmt = this.db.prepare('SELECT * FROM financial_records WHERE id = ?');
        return getStmt.get(result.lastInsertRowid);
    }
    // ===== وظائف المنتجات والمتجر =====
    async getProducts() {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare('SELECT * FROM products ORDER BY name');
        return stmt.all();
    }
    async addProduct(product) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
      INSERT INTO products (name, category, price, cost, stock, minStock, isActive)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
        const result = stmt.run(product.name, product.category, product.price, product.cost, product.stock, product.minStock, product.isActive ? 1 : 0);
        const getStmt = this.db.prepare('SELECT * FROM products WHERE id = ?');
        return getStmt.get(result.lastInsertRowid);
    }
    async updateProduct(id, product) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
      UPDATE products
      SET name = COALESCE(?, name),
          category = COALESCE(?, category),
          price = COALESCE(?, price),
          cost = COALESCE(?, cost),
          stock = COALESCE(?, stock),
          minStock = COALESCE(?, minStock),
          isActive = COALESCE(?, isActive)
      WHERE id = ?
    `);
        stmt.run(product.name || null, product.category || null, product.price || null, product.cost || null, product.stock || null, product.minStock || null, product.isActive !== undefined ? (product.isActive ? 1 : 0) : null, id);
        const getStmt = this.db.prepare('SELECT * FROM products WHERE id = ?');
        return getStmt.get(id);
    }
    async deleteProduct(id) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare('DELETE FROM products WHERE id = ?');
        const result = stmt.run(id);
        return result.changes > 0;
    }
    // ===== وظائف الإعدادات =====
    async getSettings() {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare('SELECT * FROM settings');
        const rows = stmt.all();
        const settings = {};
        rows.forEach(row => {
            settings[row.key] = row.value;
        });
        return settings;
    }
    async updateSettings(settings) {
        if (!this.db)
            throw new Error('Database not initialized');
        const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value, updatedAt)
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `);
        try {
            const transaction = this.db.transaction(() => {
                for (const [key, value] of Object.entries(settings)) {
                    stmt.run(key, value);
                }
            });
            transaction();
            return true;
        }
        catch (error) {
            console.error('Failed to update settings:', error);
            return false;
        }
    }
    async close() {
        if (this.db) {
            this.db.close();
            this.db = null;
        }
    }
}
exports.DatabaseManager = DatabaseManager;
//# sourceMappingURL=DatabaseManager.js.map