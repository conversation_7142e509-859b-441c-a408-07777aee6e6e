/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 9v1.258", key: "iwpddn" }],
  ["path", { d: "M16 3v5.46", key: "d7ew98" }],
  ["path", { d: "M21 9.118V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h5.75", key: "137t5x" }],
  [
    "path",
    {
      d: "M22 17.5c0 2.499-1.75 3.749-3.83 4.474a.5.5 0 0 1-.335-.005c-2.085-.72-3.835-1.97-3.835-4.47V14a.5.5 0 0 1 .5-.499c1 0 2.25-.6 3.12-1.36a.6.6 0 0 1 .76-.001c.875.765 2.12 1.36 3.12 1.36a.5.5 0 0 1 .5.5z",
      key: "16j3tf"
    }
  ],
  ["path", { d: "M3 15h7", key: "1qldh6" }],
  ["path", { d: "M3 9h12.142", key: "1yjd6m" }],
  ["path", { d: "M8 15v6", key: "1stoo3" }],
  ["path", { d: "M8 3v6", key: "vlvjmk" }]
];
const BrickWallShield = createLucideIcon("brick-wall-shield", __iconNode);

export { __iconNode, BrickWallShield as default };
//# sourceMappingURL=brick-wall-shield.js.map
